import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:grocery/core/constants/app_colors.dart';
import 'package:grocery/core/themes/app_themes.dart';
import '../../core/constants/app_icons.dart';

import '../../core/constants/app_defaults.dart';
import '../../core/routes/app_routes.dart';
import 'components/ad_space.dart';
import 'components/our_new_item.dart';
import 'components/popular_packs.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        // leading: Padding(
        //   padding: const EdgeInsets.only(left: 8),
        //   child: ElevatedButton(
        //     onPressed: () {
        //       Navigator.pushNamed(context, AppRoutes.drawerPage);
        //     },
        //     style: ElevatedButton.styleFrom(
        //       backgroundColor: const Color(0xFFF2F6F3),
        //       shape: const CircleBorder(),
        //     ),
        //     child: SvgPicture.asset(AppIcons.sidebarIcon),
        //   ),
        // ),
        title: const Text("ボイラクレーン安全協会"),
        //  SvgPicture.asset(
        //   "assets/images/app_logo.svg",
        //   height: 32,
        // ),
        // actions: [
        //   Padding(
        //     padding: const EdgeInsets.only(right: 8, top: 4, bottom: 4),
        //     child: ElevatedButton(
        //       onPressed: () {
        //         Navigator.pushNamed(context, AppRoutes.search);
        //       },
        //       style: ElevatedButton.styleFrom(
        //         backgroundColor: const Color(0xFFF2F6F3),
        //         shape: const CircleBorder(),
        //       ),
        //       child: SvgPicture.asset(AppIcons.search),
        //     ),
        //   ),
        // ],
      ),
      body: const SafeArea(
        child: CustomScrollView(
          slivers: [
            SliverToBoxAdapter(
              child: AdSpace(),
            ),
            SliverToBoxAdapter(
              child: PopularPacks(),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const SizedBox(
        height: 50,
        child: Center(
            child:
                Text("Copyright © Training Center Ltd. All rights reserved.")),
      ),
    );
  }
}
