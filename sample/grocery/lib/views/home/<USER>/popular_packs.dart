import 'package:flutter/material.dart';

import '../../../core/components/bundle_tile_square.dart';
import '../../../core/components/title_and_action_button.dart';
import '../../../core/constants/constants.dart';
import '../../../core/routes/app_routes.dart';

class PopularPacks extends StatelessWidget {
  const PopularPacks({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // List.generate(
        //   Dummy.bundles.length,
        //   (index) => BundleTileSquare(data: Dummy.bundles[index]),
        // ),
        // SingleChildScrollView(
        //   padding: const EdgeInsets.only(left: AppDefaults.padding),
        //   scrollDirection: Axis.,
        //   child: Row(
        //     children: List.generate(
        //       Dummy.bundles.length,
        //       (index) => Padding(
        //         padding: const EdgeInsets.only(right: AppDefaults.padding),
        //         child: BundleTileSquare(data: Dummy.bundles[index]),
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
