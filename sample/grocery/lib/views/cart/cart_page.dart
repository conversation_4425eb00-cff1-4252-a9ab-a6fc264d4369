import 'package:flutter/material.dart';

import '../../core/components/app_back_button.dart';
import '../../core/constants/app_defaults.dart';
import '../../core/routes/app_routes.dart';
import 'components/coupon_code_field.dart';
import 'components/items_totals_price.dart';
import 'components/single_cart_item_tile.dart';

class CartPage extends StatelessWidget {
  const CartPage({
    super.key,
    this.isHomePage = false,
  });

  final bool isHomePage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: isHomePage
          ? null
          : AppBar(
              leading: const AppBackButton(),
              title: const Text('Cart Page'),
            ),
      body: Safe<PERSON>rea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              const SingleCartItemTile(),
              const SingleCartItemTile(),
              const SingleCartItemTile(),
              const CouponCodeField(),
              const ItemTotalsAndPrice(),
              <PERSON><PERSON><PERSON><PERSON>(
                width: double.infinity,
                child: Padding(
                  padding: const EdgeInsets.all(AppDefaults.padding),
                  child: ElevatedButton(
                    onPressed: () {
                      // Navigator.pushNamed(context, AppRoutes.orderSuccessfull);
                      Navigator.pushNamed(context, AppRoutes.checkoutPage);
                    },
                    child: const Text('Checkout'),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
