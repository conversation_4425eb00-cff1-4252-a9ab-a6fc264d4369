import 'package:flutter/material.dart';
import 'package:flutter_website/components/components.dart';

class TypographyPage extends StatelessWidget {
  const TypographyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 32),
              child: Column(
                children: [
                  Container(
                    margin: marginBottom40,
                  ),
                  <PERSON><PERSON>(
                    alignment: Alignment.center,
                    child: Container(
                      margin: marginBottom12,
                      child: const Text("Typography", style: headlineTextStyle),
                    ),
                  ),
                  Container(
                    margin: marginBottom24,
                    child: const Text("Text styles for Flutter.dev",
                        style: bodyTextStyle),
                  ),
                  Container(
                    margin: marginBottom40,
                  ),
                  <PERSON>gn(
                    alignment: Alignment.center,
                    child: Container(
                      margin: marginBottom12,
                      child:
                          const Text("Basic Styles", style: headlineTextStyle),
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.all(24),
                    child: Divider(
                      color: textPrimary,
                      height: 1,
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      margin: marginBottom24,
                      child: const Text("Headline", style: headlineTextStyle),
                    ),
                  ),
                  Container(
                    margin: marginBottom24,
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      margin: marginBottom24,
                      child: const Text("Headline Secondary",
                          style: headlineSecondaryTextStyle),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      margin: marginBottom40,
                      child: const Text(
                          "Body text is the default text style. Use this text style for website content and paragraphs. This text is chosen to be easy and comfortable to read. As the default text style for large blocks of text, particular attention is placed on the choice of font. Some fonts are more comfortable to read than others.",
                          style: bodyTextStyle),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      margin: marginBottom24,
                      child: const Text("Button Text", style: buttonTextStyle),
                    ),
                  ),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Container(
                      margin: marginBottom40,
                      child: const Text(
                          "Body text is the default text style. Use this text style for website content and paragraphs. This text is chosen to be easy and comfortable to read. As the default text style for large blocks of text, particular attention is placed on the choice of font. Some fonts are more comfortable to read than others.",
                          style: bodyTextStyle),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      backgroundColor: Colors.white,
    );
  }
}
