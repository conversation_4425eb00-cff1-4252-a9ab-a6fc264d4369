name: Build HTML

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.PAT }}
      - uses: subosito/flutter-action@v2
        with:
          channel: stable
      - name: Clone deployment repo
        run: |
          git clone --single-branch "https://${{ secrets.PAT }}@github.com/rayliverified/Demo.git" "clone_dir"
          rm -rf clone_dir/flutterwebsites/flutterwebsite-html/
          mkdir -p clone_dir/flutterwebsites/flutterwebsite-html/
      - name: Build
        run: |
          flutter build web --release --base-href /flutterwebsites/flutterwebsite-html/ --web-renderer html --web-resources-cdn
          mv build/web/* clone_dir/flutterwebsites/flutterwebsite-html
      - name: Get current date
        id: date
        run: echo "date=$(date +'%Y%m%d')" >> $GITHUB_OUTPUT
      - name: Get time in seconds
        id: seconds
        run: echo "seconds=$(date +'%s')" >> $GITHUB_OUTPUT
      - name: Push build commit
        run: |
          cd clone_dir
          git config user.name github-actions
          git config user.email <EMAIL>
          git add *
          git commit -m "Build Flutter Website HTML ${{ steps.date.outputs.date }} (${{ steps.seconds.outputs.seconds }})"
          git pull --rebase
          git push
