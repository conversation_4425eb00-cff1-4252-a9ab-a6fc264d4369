APP_NAME='koushuu_server'
APP_ENV=local
APP_KEY='PepRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0='
APP_HOST=0.0.0.0
APP_PORT=8080
APP_SHARED=true
APP_DEBUG=true
APP_URL='http://127.0.0.1:8080'
APP_WEBSOCKET=false
APP_SECURE=false
APP_CERTIFICATE=null
APP_PRIVATE_KEY=null
APP_PRIVATE_KEY_PASSWORD=null
LANG_PATH=lang
APP_LOCALE=ja

# APP_HONAPIKEY='edqvbsu822m946b5j49nqh9nmvjxx517fjc7zjyc53mupg8729f5i99w418g6xaw'
# APP_HONENDPOINT='https://iis2.dbsj.com/seminas.local/api.aspx'
APP_HONAPIKEY='pdkuhgu2kcz04fdaj1d6kx3gxkbsvp9rfvzi81zx9b9a0mt2pwm2kbsra4xw427x'
APP_HONENDPOINT='https://www.cloudbysign.com/seminas/api.aspx'
APP_MAILURL='https://navi.bcsa.or.jp/mep'

APP_ENCKEY="0amR+*C9NTaLAOZr"
APP_ENCIV="0amR+*C9NTaLAOZr"

ISOLATE=false
ISOLATE_NUMBER=1

STORAGE=local

# CACHE_DRIVER=file
# REDIS_HOST=localhost
# REDIS_PASSWORD=null
# REDIS_PORT=6379

# databse driver mysql or postgresql or pgsql
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=boira_web
DB_USERNAME=boira
DB_PASSWORD=P@ssw0rd2025Boira
DB_SSL_MODE=true
DB_POOL=false
DB_POOL_SIZE=2

# MAIL_MAILER=smtp
# MAIL_HOST=127.0.0.1
# MAIL_PORT=465
# MAIL_ENCRYPTION=true
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=password
# MAIL_FROM_ADDRESS=<EMAIL>
# MAIL_FROM_NAME="koushuu_serve"