POST http://127.0.0.1:8081/api/v1/getkaijolist
content-type: application/json
authorization: token epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

POST http://127.0.0.1:8081/api/v1/getcategorylist
content-type: application/json
authorization: token epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

POST http://127.0.0.1:8081/api/v1/getpreflist
content-type: application/json
authorization: token epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

POST http://127.0.0.1:8081/api/v1/register
content-type: application/json

{
  "name": "buyaka",
  "email": "<EMAIL>",
  "password": "Passw0rd"
}


POST http://127.0.0.1:8081/api/v1/getkoushuulist
content-type: application/json
authorization: token epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

{
  "jimusyo_code": "001001",
  "kosyu_category_code": "1",
  "freeword": "玉掛け",
  "start_date": "2020/01/01",
  "end_date": "2024-11-21"
}

POST http://127.0.0.1:8080/api/v1/getaddress
content-type: application/json
authorization: token epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

{
  "zip_code": "338-006"
}



POST http://127.0.0.1:8081/api/v1/makesaihakkoirai
content-type: application/json
authorization: token epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

{
"SaihakkoIraiApiManager":{
  "method":"write",
  "requestdata":{
    "uketsuke_id": "1234567890",
    "name_sei": "wert",
    "name_mei": "2",
    "name_name_kana": "3",
    "old_name": "4",
    "old_name_kana": "5",
    "birth_day": "1990/12/12",
    "zip_code": "",
    "addr1": "",
    "addr2": "",
    "tel_no": "090-0900-0900",
    "mail_addr": "<EMAIL>",
    "kosyu_type": "",
    "jimusyo_code": "",
    "lost_flag": 1,
    "change_name_flag": 1,
    "damage_flag": 0,
    "other_flag": 0,
    "other_riyu": "その他情報",
    "comment": "コメント情報"
  }
}
}



POST http://127.0.0.1:8081/api/v1/openrequest
content-type: application/json
authorization: token epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

{
  "applicant_id": "",
  "applicant_name": "テスト会社1",
  "email": "<EMAIL>",
  "people_cnt": 4,
}


POST http://127.0.0.1:8081/api/v1/wdd
content-type: application/json

{
  "uid": "00000000003075"
}

## 取得予定講習区分リスト取得
POST http://127.0.0.1:8081/api/v1/koushuukubunlist
content-type: application/json
APIKEY: epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

{
  "jimusyo_code": "002002",
}


## 取得予定講習リスト取得
POST http://127.0.0.1:8081/api/v1/koushuulist
content-type: application/json
APIKEY: epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

{
  "jimusyo_code": "002002",
  "kosyu_kubun":1
}

## 取得予定受講日リスト取得
POST http://127.0.0.1:8081/api/v1/koushuudatelist
content-type: application/json
APIKEY: epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

{
  "jimusyo_code": "002002",
  "kosyu_kubun":1,
  "kosyu_code":"123456789"
}

## 取得予定受講日リスト取得
POST http://127.0.0.1:8081/api/v1/koushuukuwarilist
content-type: application/json
APIKEY: epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

{
  "jimusyo_code": "002002",
  "kosyu_kubun":1,
  "kosyu_code":"123456789",
  "kosyu_date":"1922/12/12"
}


## 取得予定受講日リスト取得
POST http://127.0.0.1:8080/api/v1/readsaihakkouketsuke
content-type: application/json
APIKEY:  PepRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

 {
    "uketsuke_id": "SA202501280006"
  }



POST http://navi.bcsa.or.jp/v1/getpreflist
 
content-type: application/json
APIKEY:  PepRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=


curl -H 'APIKEY:  PepRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=' \
      -X POST \
      http://localhost:8080/api/v1/getpreflist
      http://navi.bcsa.or.jp/api/v1/getpreflist



POST http://localhost:8080/api/v1/getkoushuuguide
 
content-type: application/json
APIKEY: epRUXSLXFJjfInE8hLmb7vIx8gBwNk-j1vVOk5X1aT0=

{
  "kosyu_code":"020001",
}

      