rm -rf ./build/*

cp ./.env.prod ./.env

vania build

cp -rf ./bin ./build/
cp -rf ./lib ./build/
cp -rf ./public ./build/
cp -rf ./storage ./build/
cp -rf ./.env.prod ./build/.env
cp -rf ./pm_start.sh ./build/
cp -rf ./pubspec.yaml ./build/
cp -rf ./pubspec.lock ./build/
cp -rf ./analysis_options.yaml ./build/

ssh -t <EMAIL> 'pm2 stop KOUSHUU; exit'

scp -rp ./build/* <EMAIL>:/home/<USER>/koushuu_server/  

ssh -t <EMAIL> 'pm2 start KOUSHUU; exit'

cp ./.env.dev ./.env

