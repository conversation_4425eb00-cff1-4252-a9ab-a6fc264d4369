import 'package:koushuu_server/app/common/encryptor.dart';

class Utils {
  //func=change
  //ek : Encrypted parameters key
  //uid : Uketsuke id
  static String makeUrl(
    String endPointUrl,
    Map<String, String>? params,
    Map<String, String>? encParams,
  ) {
    try {
      String strParam = "";
      params?.forEach((key, value) {
        if (strParam != "") strParam += "&";
        strParam += "$key=$value";
      });
      if (strParam != "") strParam = "?$strParam";

      String strEncParam = "";
      encParams?.forEach((key, value) {
        if (strEncParam != "") strEncParam += "&";
        strEncParam += "$key=$value";
      });

      if (strParam != "") {
        strParam = "$strParam&ek=${Encryptor.encryptData(strEncParam)}";
      } else {
        if (strEncParam != "") {
          strParam = "?ek=${Encryptor.encryptData(strEncParam)}";
        }
      }
      return "$endPointUrl$strParam&uid=";
    } catch (e) {
      print(e);
      return "";
    }
  }
}
