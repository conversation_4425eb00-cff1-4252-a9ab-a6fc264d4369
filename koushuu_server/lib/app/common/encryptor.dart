import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:vania/vania.dart';
import 'dart:convert'; // For URL-safe Base64 encoding

class Encryptor {
  static String encryptData(String plainText) {
    final key = encrypt.Key.fromUtf8(env("APP_ENCKEY"));
    final iv = encrypt.IV.fromUtf8(env("APP_ENCIV"));
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    final encryptedData = encrypter.encrypt(plainText, iv: iv);

    //return encryptedData.base64;
    // Use URL-safe Base64 encoding
    return base64UrlEncode(encryptedData.bytes);
  }

  static String decryptData(String encryptedText) {
    final key = encrypt.Key.fromUtf8(env("APP_ENCKEY"));
    final iv = encrypt.IV.fromUtf8(env("APP_ENCIV"));
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    //final decryptedData = encrypter.decrypt64(encryptedText, iv: iv);
    // Decode URL-safe Base64 before decryption
    final decodedBytes = base64Url.decode(encryptedText);
    final decryptedData =
        encrypter.decryptBytes(encrypt.Encrypted(decodedBytes), iv: iv);

    // return decryptedData;
    return utf8.decode(decryptedData);
  }

  static String encryptPassword(String password) {
    final bytes = utf8.encode(password);
    final hash = sha256.convert(bytes);
    return hash.toString();
  }
}
