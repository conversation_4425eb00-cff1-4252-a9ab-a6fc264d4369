import 'package:koushuu_server/app/models/applicant.dart';
import 'package:koushuu_server/app/models/email_template.dart';
import 'package:koushuu_server/app/models/setting.dart';
import 'package:vania/vania.dart';

class Db {
  static Future<String> getNewApplicationId() async {
    final obj = await Setting().query().where('id', '=', 1).first();
    final incNum = (obj!["uketsukeId"] as int) + 1;
    await Setting().query().where('id', '=', 1).update({
      'uketsukeId': incNum.toString(),
    });
    return incNum.toString();
  }

  static Future<Map<String, dynamic>> newRequest(
    String applicant_id,
    String applicant_name,
    String email,
    int people_cnt,
    String koushuu_code,
    String koushuu_number,
    String kosyu_name,
    String kosyu_date,
    String jimusyo_code,
    String jimusyo_name,
    String course_code,
    String kaijo_name,
    String applicant_tel,
    String applicant_fax,
    String applicant_addr,
  ) async {
    Map<String, dynamic> appData = <String, dynamic>{};
    try {
      appData.addAll(await Applicant().query().create({
        "applicant_id": applicant_id,
        "applicant_name": applicant_name,
        "applicant_tel": applicant_tel,
        "applicant_fax": applicant_fax,
        "applicant_addr": applicant_addr,
        "people_cnt": people_cnt,
        "email": email,
        "kosyu_code": koushuu_code,
        "kosyu_number": koushuu_number,
        "kosyu_name": kosyu_name,
        "kosyu_date": kosyu_date,
        "jimusyo_code": jimusyo_code,
        "jimusyo_name": jimusyo_name,
        "course_code": course_code,
        "kaijo_name": kaijo_name,
        "status": 0,
      }));
    } catch (e) {
      print(e);
    }

    return appData;
  }

  static Future<Map<String, dynamic>?> getEmailTemplate(
      String templateId) async {
    final obj = await EmailTemplate()
        .query()
        .where('template_id', '=', templateId)
        .first();
    return obj;
  }
}
