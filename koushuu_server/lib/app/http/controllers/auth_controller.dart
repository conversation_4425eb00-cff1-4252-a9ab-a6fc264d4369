import 'dart:io';
import 'package:koushuu_server/app/models/user.dart';
import 'package:vania/vania.dart';
import 'package:vania/src/exception/validation_exception.dart';
import 'package:koushuu_server/app/common/constants.dart' as constants;

class AuthController extends Controller {
  final Map<String, String> headers = {
    "Content-Type": "application/json",
    "token": env("APP_HONAPIKEY"),
  };

  Future<Response> register(Request request) async {
    try {
      request.validate({
        'email': 'required|string|email',
        'password': 'required'
      }, {
        'email.required': 'Email is required',
        'email.email': 'Invalid email format',
        'password.required': 'Password is required',
      });
    } catch (e) {
      if (e is ValidationException) {
        var errorMessages = e.message;
        var errorMessageList = errorMessages.values.toList();

        return Response.json(
          {
            "status": constants.API_RES_FAILED,
            "message": errorMessageList.isNotEmpty
                ? errorMessageList[0]
                : "Validation error"
          },
          HttpStatus.unauthorized,
        );
      } else {
        return Response.json(
          {
            "status": constants.API_RES_FAILED,
            "message": "An unexpected server side error",
          },
          HttpStatus.badGateway,
        );
      }
    }

    try {
      final name = request.input('name');
      final email = request.input('email');
      final password = request.input('password');

      var user = await User().query().where('email', '=', email).first();

      if (user == null) {
        return Response.json(
          {"message": "ユーザー情報見つかりませんでした。"},
          404,
        );
      }

      var hashPassword = Hash().make(password);
      try {
        await User().query().where("email", "=", email).update({
          "password": hashPassword,
          "updated_at": DateTime.now(),
        });
      } catch (e) {
        print(e);
        return Response.json({
          "status": constants.API_RES_FAILED,
          "message": "パスワードの設定出来ませんでした。",
        });
      }

      return Response.json({
        "status": constants.API_RES_SUCCESS,
        "message": "パスワードの設定完了しました。",
      });
    } catch (e) {
      return Response.json(
        {
          "status": constants.API_RES_FAILED,
          "message": "サーバーエラーが発生しました。",
        },
        HttpStatus.badGateway,
      );
    }
  }

  Future<Response> login(Request request) async {
    try {
      request.validate({
        'email': 'required|string|email',
        'password': 'required'
      }, {
        'email.required': 'Email is required',
        'email.email': 'Invalid email format',
        'password.required': 'Password is required',
      });
    } catch (e) {
      if (e is ValidationException) {
        var errorMessages = e.message;
        var errorMessageList = errorMessages.values.toList();

        return Response.json(
          {
            "message": errorMessageList.isNotEmpty
                ? errorMessageList[0]
                : "Validation error"
          },
          HttpStatus.unauthorized,
        );
      } else {
        return Response.json(
          {"message": "An unexpected server side error"},
          HttpStatus.badRequest,
        );
      }
    }

    try {
      final email = request.input('email');
      final password = request.input('password');

      var user = await User().query().where('email', '=', email).first();

      if (user != null) {
        return Response.json(
          {"message": "Email not found"},
          HttpStatus.notFound,
        );
      }

      if (!Hash().verify(password, user!["password"])) {
        return Response.json(
          {"message": "Myabe password is incorrect!"},
          HttpStatus.badRequest,
        );
      }

      final auth = Auth().login(user);
      final token = auth.createToken(expiresIn: Duration(days: 1));
      //String accessToken = token["access_token"];

      return Response.json({
        "status": constants.API_RES_SUCCESS,
        "message": "Login success",
        "token": token,
      });
    } catch (e) {
      return Response.json(
        {"message": "An unexpected server side error"},
        HttpStatus.badRequest,
      );
    }
  }
}

final AuthController authController = AuthController();
