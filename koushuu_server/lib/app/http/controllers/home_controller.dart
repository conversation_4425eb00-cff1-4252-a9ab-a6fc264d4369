import 'package:koushuu_server/app/common/db.dart';
import 'package:koushuu_server/app/common/encryptor.dart';
import 'package:koushuu_server/app/common/utils.dart';
import 'package:koushuu_server/app/http/controllers/external_controller.dart';
import 'package:koushuu_server/app/models/applicant.dart';
import 'package:koushuu_server/app/models/members.dart';
import 'package:koushuu_server/app/models/user.dart';
import 'package:vania/vania.dart';
import 'package:koushuu_server/app/common/constants.dart' as constants;
import 'package:intl/intl.dart';

class HomeController extends Controller {
  Future<Response> index() async {
    return Response.json({"msg": 'welcome'.trans()});
  }

  Future<Response> openRequest(Request req) async {
    print(req.body.toString());
    //make Application
    String strUrl = "";
    try {
      dbTransaction((Connection con) async {
        final appData = await Applicant()
            .query()
            .where("email", "=", req.string("email"))
            .first();

        if (appData != null) {
          return Response.json({
            "status": constants.API_RES_FAILED,
            "message": "既に登録されています。",
          });
        }
        final obj = await Db.newRequest(
          req.string("applicant_id"),
          req.string("applicant_name"),
          req.string("email"),
          req.input("people_cnt"),
          req.string("kousyu_code"),
          req.string("kousyu_number"),
          req.string("kousyu_name"),
          req.string("kousyu_date"),
          req.string("jimusyo_code"),
          req.string("jimusyo_name"),
          req.string("course_code"),
          req.string("kaijo_name"),
          req.string("applicant_tel"),
          req.string("applicant_fax"),
          req.string("applicant_addr"),
        );

        //prepare mail url params
        Map<String, String> mailUrlParam = {
          constants.STEPKEY: constants.STEP1,
          "id": obj["id"].toString(),
          "email": obj["email"].toString(),
          "applicant_id": req.string("applicant_id"),
          "applicant_name": req.string("applicant_name"),
          "people_cnt": req.input("people_cnt").toString()
        };

        strUrl = Utils.makeUrl(env("APP_MAILURL"), null, mailUrlParam);

        //Register user
        await User().query().insert({
          "name": req.string("applicant_name"),
          "email": req.string("email"),
          "application_id": obj["id"],
          "created_at": DateTime.now(),
          "updated_at": DateTime.now(),
        });

        int pcnt = req.input("people_cnt") != "" ? req.input("people_cnt") : 0;

        for (var i = 0; i < pcnt; i++) {
          //Register participant for application
          final objMember = await Member().query().create({
            "application_id": obj["id"].toString(), // 申請ID
            "uketsuke_id": constants.DB_NULL_VALUE, // 受付ID
            "kosyu_number": req.string("kousyu_number"), // 講習番号
            "kosyu_code": req.string("kousyu_code"), // 講習コード
            "kosyu_date": constants.DB_NULL_DATE, // 講習日付
            "course_code": req.string("course_code"), // コースコード
            "jimusyo_code": constants.DB_NULL_INT, // 事務所コード
            "kaijyo_code": constants.DB_NULL_INT, // 会場コード
            "applicant_number": obj["id"].toString(), // 申込番号
            "uketsuke_date": constants.DB_NULL_DATE, // 受付日付
            "applicant_code": constants.DB_NULL_INT, // 申込者コード
            "name1": constants.DB_NULL_VALUE, // 氏名1
            "name2": constants.DB_NULL_VALUE, // 氏名2
            "name_kana": constants.DB_NULL_VALUE, // 氏名カナ
            "old_or_common_name_type": constants.DB_NULL_INT, // 旧姓通称区分
            "name3": constants.DB_NULL_VALUE, // 氏名3
            "birth_day": constants.DB_NULL_DATE, // 生年月日
            "zip_code": constants.DB_NULL_VALUE, // 郵便番号
            "addr1": constants.DB_NULL_VALUE, // 現住所1
            "addr2": constants.DB_NULL_VALUE, // 現住所2
            "tel_no": constants.DB_NULL_VALUE, // 電話番号
            "mail_addr": constants.DB_NULL_VALUE, // メールアドレス
            "sinkoku_jimusyo_code": constants.DB_NULL_INT, // 申告事務所コード
            "sinkoku_kosyu_kubun": constants.DB_NULL_INT, // 申告講習区分
            "sinkoku_kosyu_date": constants.DB_NULL_VALUE, // 申告講習日付
            "sinkoku_kuwari_kubun": constants.DB_NULL_INT, // 申告区割区分
            "unacquired_kosyu_code": constants.DB_NULL_INT, // 未取得講習コード
            "declaration_party_name": constants.DB_NULL_VALUE, // 申告団体名
            "declaration_date": constants.DB_NULL_DATE, // 申告交付日付
            "soufu_kubun": constants.DB_NULL_INT, // 送付先区分
            "url": strUrl, // 入力フォームURL
            "image_photo": "", // 本人写真画像データ(Base64)
            "filename_photo": "", // 本人写真ファイル名
            "filetype_photo": "", // 本人写真ファイル形式(MIMEType)
            "image_kakunin_omote": "", // 本人確認(表)画像データ(Base64)
            "filename_kakunin_omote": "", // 本人確認(表)ファイル名
            "filetype_kakunin_omote": "", // 本人確認(表)ファイル形式(MIMEType)
            "image_kakunin_ura": "", // 本人確認(裏)画像データ(Base64)
            "filename_kakunin_ura": "", // 本人確認(裏)ファイル名
            "filetype_kakunin_ura": "", // 本人確認(裏)ファイル形式(MIMEType)
            "image_kakunin_atsumi": "", // 本人確認(厚み)画像データ(Base64)
            "filename_kakunin_atsumi": "", // 本人確認(厚み)ファイル名
            "filetype_kakunin_atsumi": "", // 本人確認(厚み)ファイル形式(MIMEType)
            "image_license1": "", // 受講資格1画像データ(Base64)
            "filename_license1": "", // 受講資格1ファイル名
            "filetype_license1": "", // 受講資格1ファイル形式(MIMEType)
            "image_license2": "", // 受講資格2画像データ(Base64)
            "filename_license2": "", // 受講資格2ファイル名
            "filetype_license2": "", // 受講資格2ファイル形式(MIMEType)
            "image_license3": "", // 受講資格3画像データ(Base64)
            "filename_license3": "", // 受講資格3ファイル名
            "filetype_license3": "", // 受講資格3ファイル形式(MIMEType)
            "created_at": DateTime.now(),
            "updated_at": DateTime.now(),
          });

          //Save to honsystem
          final objExternalData = await externalController.makeUketsukeWithObj({
            "uketsuke_id": constants.DB_NULL_VALUE, // 受付ID
            // "kosyu_number": constants.DB_NULL_INT, // 講習番号
            // "kosyu_code": constants.DB_NULL_INT, // 講習コード
            "kosyu_number": req.string("kousyu_number"), // 講習番号
            "kosyu_code": req.string("kousyu_code"), // 講習コード
            "kosyu_date": constants.DB_NULL_DATE, // 講習日付
            "course_code": req.string("course_code"), // コースコード
            "jimusyo_code": constants.DB_NULL_INT, // 事務所コード
            "kaijyo_code": constants.DB_NULL_INT, // 会場コード
            "applicant_number": obj["id"].toString(), // 申込番号
            "applicant_name": req.string("applicant_name"), //申込者名
            "applicant_addr": req.string("applicant_addr"), //申込者住所
            "applicant_tel": req.string("applicant_tel"), //申込者電話番号
            "applicant_fax": req.string("applicant_fax"), //申込者FAX番号
            "applicant_mail": req.string("email"), //申込者メールアドレス
            "uketsuke_date": constants.DB_NULL_DATE, // 受付日付
            "applicant_code": constants.DB_NULL_INT, // 申込者コード
            "name1":
                constants.DB_NULL_VALUE + " " + constants.DB_NULL_VALUE, // 氏名1
            "name2": constants.DB_NULL_VALUE, // 氏名2
            "name_kana": constants.DB_NULL_VALUE, // 氏名カナ
            "old_or_common_name_type": constants.DB_NULL_INT, // 旧姓通称区分
            "name3": constants.DB_NULL_VALUE, // 氏名3
            "birth_day": constants.DB_NULL_DATE, // 生年月日
            "zip_code": constants.DB_NULL_VALUE, // 郵便番号
            "addr1": constants.DB_NULL_VALUE, // 現住所1
            "addr2": constants.DB_NULL_VALUE, // 現住所2
            "tel_no": constants.DB_NULL_VALUE, // 電話番号
            "mail_addr": constants.DB_NULL_VALUE, // メールアドレス
            "sinkoku_jimusyo_code": constants.DB_NULL_INT, // 申告事務所コード
            "sinkoku_kosyu_kubun": constants.DB_NULL_INT, // 申告講習区分
            "sinkoku_kosyu_date": constants.DB_NULL_DATE, // 申告講習日付
            "sinkoku_kuwari_kubun": constants.DB_NULL_INT, // 申告区割区分
            "unacquired_kosyu_code": constants.DB_NULL_INT, // 未取得講習コード
            "declaration_party_name": constants.DB_NULL_VALUE, // 申告団体名
            "declaration_date": constants.DB_NULL_DATE, // 申告交付日付
            "soufu_kubun": constants.DB_NULL_VALUE, // 送付先区分
            "url": strUrl, // 入力フォームURL
            "image_photo": "", // 本人写真画像データ(Base64)
            "filename_photo": "", // 本人写真ファイル名
            "filetype_photo": "", // 本人写真ファイル形式(MIMEType)
            "image_kakunin_omote": "", // 本人確認(表)画像データ(Base64)
            "filename_kakunin_omote": "", // 本人確認(表)ファイル名
            "filetype_kakunin_omote": "", // 本人確認(表)ファイル形式(MIMEType)
            "image_kakunin_ura": "", // 本人確認(裏)画像データ(Base64)
            "filename_kakunin_ura": "", // 本人確認(裏)ファイル名
            "filetype_kakunin_ura": "", // 本人確認(裏)ファイル形式(MIMEType)
            "image_kakunin_atsumi": "", // 本人確認(厚み)画像データ(Base64)
            "filename_kakunin_atsumi": "", // 本人確認(厚み)ファイル名
            "filetype_kakunin_atsumi": "", // 本人確認(厚み)ファイル形式(MIMEType)
            "image_license1": "", // 受講資格1画像データ(Base64)
            "filename_license1": "", // 受講資格1ファイル名
            "filetype_license1": "", // 受講資格1ファイル形式(MIMEType)
            "image_license2": "", // 受講資格2画像データ(Base64)
            "filename_license2": "", // 受講資格2ファイル名
            "filetype_license2": "", // 受講資格2ファイル形式(MIMEType)
            "image_license3": "", // 受講資格3画像データ(Base64)
            "filename_license3": "", // 受講資格3ファイル名
            "filetype_license3": "", // 受講資格3ファイル形式(MIMEType)
          });

          print("1-123123123 objExternalData");
          print(objExternalData.toString());

          if (objExternalData["status"] == constants.API_RES_SUCCESS) {
            final uketsukeId = objExternalData["responsedata"]["uketsuke_id"];
            final memberId = objMember['id'];
            //Update uketsuke_id from honsystems response to own server
            await Member().query().where('id', '=', memberId).update({
              "uketsuke_id": uketsukeId,
            });
          } else {
            //print(objExternalData);
            //throw Exception('Failed to load data OpenRequest');
            return Response.json({
              "status": constants.API_RES_FAILED,
              "message": objExternalData["message"],
            });
          }
        }
      });
    } catch (e) {
      print(e);
      //throw Exception('Failed to load data OpenRequest');
      return Response.json({
        "status": constants.API_RES_FAILED,
        "message": e.toString(),
      });
    }

    //set mail link param prepare
    Map<String, dynamic> extraAttributes =
        req.json("extra_attributes") as Map<String, dynamic>;
    extraAttributes.update(
      "KEY#9",
      (value) => strUrl,
      ifAbsent: () => strUrl,
    );

    //then sent email
    return await externalController.sendEmail(
      "",
      req.string("email"),
      req.string("subject"),
      constants.EMAIL_MOUSHIKOMI,
      extraAttributes,
      null,
    );
  }

  Future<Response> checkUserExits(Request req) async {
    print(req.body.toString());
    final obj =
        await User().query().where("email", "=", req.string("email")).first();

    if (obj!.isNotEmpty) {
      return Response(
        data: {
          "status": constants.API_RES_SUCCESS,
          "exists": "1",
          "pass": obj["password"] == null ? "0" : "1"
        },
        responseType: ResponseType.json,
      );
    }
    return Response(
      data: {"status": constants.API_RES_SUCCESS, "exists": "0", "pass": "0"},
      responseType: ResponseType.json,
    );
  }

  Future<Response> login(Request request) async {
    request.validate({
      'email': 'required',
      'password': 'required',
    }, {
      'email.required': 'メールアドレスが必須です。',
      'password.required': 'パスワードが必須です。',
    });

    final obj = await User()
        .query()
        .select(['id', 'name', 'email', 'application_id'])
        .where("email", "=", request.string("email"))
        .where("password", "=",
            Encryptor.encryptPassword(request.string("password")))
        .first();

    if (obj != null) {
      //if pariticipant list selected? check application info with email
      final objApp = await Applicant()
          .query()
          .where("email", "=", request.string("email"))
          .orderBy("id", 'desc')
          .first();

      if (objApp != null) {
        obj.addAll({"list": "1"});
      } else {
        obj.addAll({"list": "0"});
      }

      //if pariticipant check member info with email
      final objMember = await Member()
          .query()
          .where("mail_addr", "=", request.string("email"))
          .orderBy("id", 'desc')
          .first();

      if (objMember != null) {
        obj.addAll({"member_id": objMember["id"]});
      } else {
        obj.addAll({"member_id": "0"});
      }

      return Response.json({
        "status": constants.API_RES_SUCCESS,
        "data": obj,
      });
    }
    return Response.json({
      "status": constants.API_RES_FAILED,
      "message": "メールアドレスまたはパスワードが正しくありません。",
    });
  }

  Future<Response> setPassword(Request req) async {
    // print(req.body.toString());
    final obj =
        await User().query().where("email", "=", req.string("email")).first();

    if (obj != null) {
      if (obj["password"] == null || obj["password"] == "") {
        try {
          await User().query().where("email", "=", req.string("email")).update(
              {"password": Encryptor.encryptPassword(req.string("password"))});
        } catch (e) {
          print(e);
          return Response(
            data: {"status": constants.API_RES_FAILED},
            responseType: ResponseType.json,
          );
        }
      }

      return Response(
        data: {"status": constants.API_RES_SUCCESS},
        responseType: ResponseType.json,
      );
    }
    return Response(
      data: {"status": constants.API_RES_FAILED},
      responseType: ResponseType.json,
    );
  }

  Future<Response> getParticipants(Request request) async {
    // print(request.body.toString());
    request.validate({
      'application_id': 'required',
    }, {
      'application_id.required': '申請IDが必須です。',
    });

    final objApp = await Applicant()
        .query()
        .where("id", "=", request.input("application_id"))
        .first();

    if (objApp != null) {
      final objList = await Member()
          .query()
          .select(
              ["id", "name1", "name2", "mail_addr", "status", "uketsuke_id"])
          .where("application_id", "=", request.string("application_id"))
          .get();

      // print(objList.toString());

      return Response.json({
        "status": constants.API_RES_SUCCESS,
        "data": {"list": objList, "people_cnt": objApp["people_cnt"]}
      });
    }
    return Response.json({
      "status": constants.API_RES_FAILED,
      "message": "データ取得できませんでした。",
      "data": "",
    });
  }

  Future<Response> getParticipant(Request request) async {
    // request.validate({
    //   'uketsuke_id': 'required',
    //   'participant_id': 'required',
    // }, {
    //   'uketsuke_id.required': '受付IDが必須です。',
    //   'participant_id.required': '参加者IDが必須です。',
    // });
    print(request.body);

    if (request.string("participant_id") == "" &&
        request.string("uketsuke_id") == "") {
      return Response.json({
        "status": constants.API_RES_FAILED,
        "responsedata": "",
      });
    }

    if (request.string("uketsuke_id") != "") {
      return externalController.readUketsuke(request);
    } else if (request.string("participant_id") != "") {
      final obj = await Member()
          .query()
          .where("id", "=", request.input("participant_id"))
          .first();

      // String resReplaced = "";
      // if (obj != null) {
      //   resReplaced = obj.toString();
      //   resReplaced = resReplaced.replaceAll(constants.DB_NULL_VALUE, "");
      //   resReplaced = resReplaced.replaceAll(constants.DB_NULL_DATETIME, "");
      //   resReplaced = resReplaced.replaceAll(constants.DB_NULL_DATE, "");
      // }

      return Response.json({
        "status": constants.API_RES_SUCCESS,
        "responsedata": obj,
      });
    }

    return Response.json({
      "status": constants.API_RES_FAILED,
      "responsedata": "",
    });
  }

  Future<Response> setParticipant(Request request) async {
    request.validate({
      'application_id': 'required',
      'uketsuke_id': 'required',
      'email': 'required',
      'name': 'required',
    }, {
      'application_id.required': '申請IDが必須です。',
      'uketsuke_id.required': '受付IDが必須です。',
      'email.required': 'メールアドレスが必須です。',
      'name.required': '氏名が必須です。',
    });

    print(request.body);

    try {
      final nms = request.string("name").split(" ");

      //Check user exists
      final userInfo = await User()
          .query()
          .where("email", "=", request.string("email"))
          .where("application_id", "=", request.string("application_id"))
          .first();

      //Register user
      if (userInfo == null) {
        await User().query().insert({
          "name": request.string("name"),
          "email": request.string("email"),
          "application_id": request.string("application_id"),
          "created_at": DateTime.now(),
          "updated_at": DateTime.now(),
        });
      }

      //Set member info for uketsuke_id
      await Member()
          .query()
          .where("application_id", "=", request.string("application_id"))
          .where("uketsuke_id", "=", request.string("uketsuke_id"))
          .update({
        "name1": nms[0],
        "name2": nms.length > 1 ? nms[1] : "",
        "mail_addr": request.string("email"),
      });

      //get member info
      final mmbr = await Member()
          .query()
          .where("application_id", "=", request.string("application_id"))
          .where("uketsuke_id", "=", request.string("uketsuke_id"))
          .first();

      //Send email for this user
      //set mail link param prepare
      Map<String, String> mailUrlParam = {
        constants.STEPKEY: constants.STEP2,
        "application_id": request.string("application_id"),
        "uketsuke_id": request.string("uketsuke_id"),
        "participant_id": mmbr!["id"].toString(),
        "email": request.string("email"),
        "applicant_name": request.string("applicant_name"),
      };

      final strUrl = Utils.makeUrl(env("APP_MAILURL"), null, mailUrlParam);

      Map<String, dynamic> extraAttributes = {
        "KEY#1": request.string("applicant_name"),
        "KEY#2": request.string("name"),
        "KEY#3": strUrl //+ request.string("uketsuke_id"),
      };

      //then sent email
      Response resEmail = await externalController.sendEmail(
        "",
        request.string("email"),
        "",
        constants.EMAIL_INPUT_REQ,
        extraAttributes,
        null,
      );

      if (resEmail.data["status"] == constants.API_RES_SUCCESS) {
        return Response.json({
          "status": constants.API_RES_SUCCESS,
          "message": "本人情報登録ためのおメールを送信しました。",
        });
      } else {
        return Response.json({
          "status": constants.API_RES_FAILED,
          "message": resEmail.data["message"], //"本人情報登録ためのおメールをできませんでした。",
        });
      }
    } catch (e) {
      return Response.json({
        "status": constants.API_RES_FAILED,
        "message": e,
      });
    }
  }

  Future<Response> crtParticipant(Request request) async {
    //TODO check is this email and application_id relation

    try {
      //Register user
      await Member()
          .query()
          .where('id', '=', request.string("id"))
          .orWhere('uketsuke_id', '=', request.string("uketsuke_id"))
          .update({
        // "application_id": request.string("application_id"), // 受付ID
        // "uketsuke_id": request.string("uketsuke_id"), // 受付ID
        "kosyu_number": request.string("kosyu_number"), // 講習番号
        "kosyu_code": request.string("kosyu_code"), // 講習コード
        "kosyu_date": request.string("kosyu_date"), // 講習日付
        "course_code": request.string("course_code"), // コースコード
        "jimusyo_code": request.string("jimusyo_code"), // 事務所コード
        "kaijyo_code": request.string("kaijyo_code"), // 会場コード
        "applicant_number": request.string("applicant_number"), // 申込番号
        "uketsuke_date": DateFormat('yyyy/MM/dd')
            .format(DateTime.now()), //request.string("uketsuke_date"), // 受付日付
        "applicant_code": request.integer("applicant_code"), // 申込者コード
        "name1": request.string("name1"), // 氏名1
        "name2": request.string("name2"), // 氏名2
        "name_kana": request.string("name_kana"), // 氏名カナ
        "old_or_common_name_type":
            request.string("old_or_common_name_type"), // 旧姓通称区分
        "name3": constants.DB_NULL_VALUE, // 氏名3
        "birth_day": request.string("birth_day"), // 生年月日
        "zip_code": request.string("zip_code"), // 郵便番号
        "addr1": request.string("addr1"), // 現住所1
        "addr2": request.string("addr2"), // 現住所2
        "tel_no": request.string("tel_no"), // 電話番号
        "mail_addr": request.string("mail_addr"), // メールアドレス
        "sinkoku_jimusyo_code":
            request.string("sinkoku_jimusyo_code"), // 申告事務所コード
        "sinkoku_kosyu_kubun": request.string("sinkoku_kosyu_kubun"), // 申告講習区分
        "sinkoku_kosyu_date": request.string("sinkoku_kosyu_date"), // 申告講習日付
        "sinkoku_kuwari_kubun":
            request.string("sinkoku_kuwari_kubun"), // 申告区割区分
        "unacquired_kosyu_code":
            request.string("unacquired_kosyu_code"), // 未取得講習コード
        "sinkoku_jimusyo_code_title":
            request.string("sinkoku_jimusyo_code_title"), // 申告事務所コード
        "sinkoku_kosyu_kubun_title":
            request.string("sinkoku_kosyu_kubun_title"), // 申告講習区分
        "sinkoku_kuwari_kubun_title":
            request.string("sinkoku_kuwari_kubun_title"), // 申告区割区分
        "unacquired_kosyu_code_title":
            request.string("unacquired_kosyu_code_title"), // 未取得講習コード
        "declaration_party_name":
            request.string("declaration_party_name"), // 申告団体名
        "declaration_date": request.string("declaration_date"), // 申告交付日付
        "soufu_kubun": request.string("soufu_kubun"), // 送付先区分
        "url": "", // 入力フォームURL
        "image_photo": request.string("image_photo"), // 本人写真画像データ(Base64)
        "filename_photo": request.string("filename_photo"), // 本人写真ファイル名
        "filetype_photo":
            request.string("filetype_photo"), // 本人写真ファイル形式(MIMEType)
        "image_kakunin_omote":
            request.string("image_kakunin_omote"), // 本人確認(表)画像データ(Base64)
        "filename_kakunin_omote":
            request.string("filename_kakunin_omote"), // 本人確認(表)ファイル名
        "filetype_kakunin_omote":
            request.string("filetype_kakunin_omote"), // 本人確認(表)ファイル形式(MIMEType)
        "image_kakunin_ura":
            request.string("image_kakunin_ura"), // 本人確認(裏)画像データ(Base64)
        "filename_kakunin_ura":
            request.string("filename_kakunin_ura"), // 本人確認(裏)ファイル名
        "filetype_kakunin_ura":
            request.string("filetype_kakunin_ura"), // 本人確認(裏)ファイル形式(MIMEType)
        "image_kakunin_atsumi":
            request.string("image_kakunin_atsumi"), // 本人確認(厚み)画像データ(Base64)
        "filename_kakunin_atsumi":
            request.string("filename_kakunin_atsumi"), // 本人確認(厚み)ファイル名
        "filetype_kakunin_atsumi": request
            .string("filetype_kakunin_atsumi"), // 本人確認(厚み)ファイル形式(MIMEType)
        "image_license1":
            request.string("image_license1"), // 受講資格1画像データ(Base64)
        "filename_license1": request.string("filename_license1"), // 受講資格1ファイル名
        "filetype_license1":
            request.string("filetype_license1"), // 受講資格1ファイル形式(MIMEType)
        "image_license2":
            request.string("image_license2"), // 受講資格2画像データ(Base64)
        "filename_license2": request.string("filename_license2"), // 受講資格2ファイル名
        "filetype_license2":
            request.string("filetype_license2"), // 受講資格2ファイル形式(MIMEType)
        "image_license3":
            request.string("image_license3"), // 受講資格3画像データ(Base64)
        "filename_license3": request.string("filename_license3"), // 受講資格3ファイル名
        "filetype_license3":
            request.string("filetype_license3"), // 受講資格3ファイル形式(MIMEType)
        "updated_at": DateTime.now(),
        "status": "1", // 連絡事項
      });
    } catch (e) {
      print(e);
      return Response.json({
        "status": constants.API_RES_FAILED,
        "message": e,
      });
    }

    return Response.json({
      "status": constants.API_RES_SUCCESS,
      "message": "情報登録完了しました。",
    });
  }

  Future<Response> allDone(Request request) async {
    request.validate({
      'application_id': 'required',
    }, {
      'application_id.required': '申込IDが必須です。',
    });

    try {
      final aid = request.string("application_id");
      //Not completed users check
      final notCompletedMemberList = await Member()
          .query()
          .where("application_id", "=", aid)
          .where("status", "=", 0)
          .get();

      if (notCompletedMemberList.isNotEmpty) {
        return Response.json({
          "status": constants.API_RES_FAILED,
          "message": "全ての受講者の情報が登録されていません。",
        });
      }

      //Send all user data to remote server
      final memberList = await Member()
          .query()
          .where("application_id", "=", aid)
          .where("status", "=", 1)
          .get();

      for (var m in memberList) {
        final res = await externalController.makeUketsukeWithObj(m);
        if (res["status"] != constants.API_RES_SUCCESS) {
          return Response.json({
            "status": constants.API_RES_FAILED,
            "message": "全ての受講者の情報が登録できませんでした。",
          });
        }
      }

      final appData = await Applicant().query().where("id", "=", aid).first();
      final Map<String, dynamic> extraAttributes = {
        "KEY#1": appData?["applicant_name"],
        "KEY#2": appData?["kosyu_name"],
        "KEY#3": appData?["jimusyo_name"],
        "KEY#4": appData?["kosyu_date"],
        "KEY#5": appData?["people_cnt"].toString(),
      };

      //then sent email
      await externalController.sendEmail(
        "",
        appData!["email"],
        "",
        constants.EMAIL_MOUSHIKOMI_ALLDONE,
        extraAttributes,
        null,
      );

      //Delete members for selected application
      await Member().query().where("application_id", "=", aid).delete();
      //Delete users for selected application
      await User().query().where("application_id", "=", aid).delete();
      //Delete selected application
      await Applicant().query().where("id", "=", aid).delete();

      return Response.json({
        "status": constants.API_RES_SUCCESS,
        "message": "申請情報全て削除されました。",
      });
    } catch (e) {
      return Response.json({
        "status": constants.API_RES_FAILED,
        "message": e,
      });
    }
  }

  Future<Response> deleteData(Request request) async {
    request.validate({
      'uid': 'required',
    }, {
      'uid.required': '受付IDが必須です。',
    });

    try {
      final uid = request.string("uid");

      //Delete members for selected uketsuke_id
      await Member().query().where("uketsuke_id", "=", uid).delete();

      return Response.json({
        "status": constants.API_RES_SUCCESS,
        "message": "受付データ削除しました。",
      });
    } catch (e) {
      return Response.json({
        "status": constants.API_RES_FAILED,
        "message": e,
      });
    }
  }
}

final HomeController homeController = HomeController();
