import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:koushuu_server/app/common/utils.dart';
import 'package:koushuu_server/app/models/email_template.dart';
import 'package:koushuu_server/app/common/constants.dart' as constants;
import 'package:vania/vania.dart';

class ExternalController extends Controller {
  final Map<String, String> headers = {
    "Content-Type": "application/json",
    "token": env("APP_HONAPIKEY"),
  };

  Future<Response> getAddressWithPostalCode(Request request) async {
    final Map<String, dynamic> body = {
      "MasterApiManager": {
        "method": "findaddress",
        "requestdata": {
          "zip_code": request.string('zip_code'),
        }
      }
    };
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('データ取得にエラーが発生しました。');
    }
  }

  Future<Response> getKaijoList() async {
    final Map<String, dynamic> body = {
      "MasterApiManager": {
        "method": "kaijyolist",
        "requestdata": {
          "jimusyo_code": "",
          //"事務所コード": "",
        }
      }
    };
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      //print(response.body);
      // If the server did return a 200 OK response,
      // then parse the JSON.
      return Response.json(jsonDecode(response.body));
    } else {
      // If the server did not return a 200 OK response,
      // then throw an exception.
      throw Exception('Failed to load album');
    }
  }

  //取得予定講習区分リスト取得
  Future<Response> getKoushuuKubunList(Request request) async {
    final Map<String, dynamic> body = {
      "MasterApiManager": {
        "method": "sinkokukosyukubunlist",
        "requestdata": {
          "jimusyo_code": request.string("jimusyo_code"),
        }
      }
    };
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      //print(response.body);
      // If the server did return a 200 OK response,
      // then parse the JSON.
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load album');
    }
  }

  //取得予定講習リスト取得
  Future<Response> getKoushuuList(Request request) async {
    final Map<String, dynamic> body = {
      "MasterApiManager": {
        "method": "sinkokukosyulist",
        "requestdata": {
          "jimusyo_code": request.string("jimusyo_code"),
          "kosyu_kubun": request.string("kosyu_kubun"),
        }
      }
    };
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      //print(response.body);
      // If the server did return a 200 OK response,
      // then parse the JSON.
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load album');
    }
  }

  //取得予定受講日リスト取得
  Future<Response> getKoushuuDateList(Request request) async {
    final Map<String, dynamic> body = {
      "MasterApiManager": {
        "method": "sinkokukosyudatelist",
        "requestdata": {
          "jimusyo_code": request.string("jimusyo_code"),
          "kosyu_kubun": request.input("kosyu_kubun"),
          "kosyu_code": request.string("kosyu_code"),
        }
      }
    };
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      //print(response.body);
      // If the server did return a 200 OK response,
      // then parse the JSON.
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load album');
    }
  }

  //取得予定区割区分リスト取得
  Future<Response> getKoushuuKuwariList(Request request) async {
    final Map<String, dynamic> body = {
      "MasterApiManager": {
        "method": "sinkokukuwarilist",
        "requestdata": {
          "jimusyo_code": request.string("jimusyo_code"),
          "kosyu_kubun": request.input("kosyu_kubun"),
          "kosyu_code": request.string("kosyu_code"),
          "kosyu_date": request.string("kosyu_date"),
        }
      }
    };
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      //print(response.body);
      // If the server did return a 200 OK response,
      // then parse the JSON.
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load album');
    }
  }

  Future<Response> getPrefList() async {
    final Map<String, dynamic> body = {
      "MasterApiManager": {"method": "preflist", "requestdata": {}}
    };
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load album');
    }
  }

  Future<Response> getCategoryList() async {
    final Map<String, dynamic> body = {
      "MasterApiManager": {
        "method": "kosyucategorylist",
        "requestdata": {},
      }
    };
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      //print(response.body);
      // If the server did return a 200 OK response,
      // then parse the JSON.
      return Response.json(jsonDecode(response.body));
    } else {
      // If the server did not return a 200 OK response,
      // then throw an exception.
      throw Exception('Failed to load album');
    }
  }

  Future<Response> searchKoushuu(Request request) async {
    request.validate({
      'jimusyo_code': 'required',
      'kosyu_category_code': 'required',
      'start_date': 'required',
      'end_date': 'required',
    }, {
      'jimusyo_code.required': '事務コードが必須です。',
      'kosyu_category_code.required': '講習カテゴリが必須です。',
      'start_date.required': '講習開始日が必須です。',
      'end_date.required': '講習終了日が必須です。',
    });

    final Map<String, dynamic> body = {
      "MasterApiManager": {
        "method": "kosyulist",
        "requestdata": {
          "jimusyo_code": request.string('jimusyo_code'),
          "kosyu_category_code": request.string('kosyu_category_code'),
          "freeword": request.string('freeword'),
          "start_date": request.string('start_date'), //"YYYY/MM/DD",
          "end_date": request.string('end_date'), //"YYYY/MM/DD"
        }
      }
    };

    String jsonBody = json.encode(body);
    print("----- REQUEST -----");
    print(jsonBody);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    print(jsonEncode(response.body));
    //print(response.body);

    if (response.statusCode == 200) {
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<Response> getKoushuuGuide(Request request) async {
    request.validate({
      'kosyu_code': 'required',
    }, {
      'kosyu_code.required': '講習コードが必須です。',
    });

    final Map<String, dynamic> body = {
      "MasterApiManager": {
        "method": "kosyuguide",
        "requestdata": {"kosyu_code": request.string('kosyu_code')}
      }
    };

    print(jsonEncode(body));

    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );
    print("---------");

    print(jsonEncode(response.body));

    if (response.statusCode == 200) {
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<Response> readUketsuke(Request request) async {
    request.validate({
      'uketsuke_id': 'required',
    }, {
      'uketsuke_id.required': '受付IDが必須です。',
    });

    final Map<String, dynamic> body = {
      "UketsukeApiManager": {
        "method": "read",
        "requestdata": {
          "uketsuke_id": request.string('uketsuke_id'),
        }
      }
    };
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );
    print(response.body);
    if (response.statusCode == 200) {
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<Response> makeUketsuke(Request request) async {
    final Map<String, dynamic> body = {
      "UketsukeApiManager": {
        "method": "write",
        "requestdata": {
          "uketsuke_id": request.string('uketsuke_id'), // 受付ID
          "kosyu_number": request.string('kosyu_number'), // 講習番号
          "kosyu_code": request.string('kosyu_code'), // 講習コード
          "kosyu_date": request.string('kosyu_date'), // 講習日付
          "course_code": request.string('course_code'), // コースコード
          "jimusyo_code": request.string('jimusyo_code'), // 事務所コード
          "kaijyo_code": request.string('kaijyo_code'), // 会場コード
          "applicant_number": request.string('applicant_number'), // 申込番号
          "applicant_code": request.string('applicant_code'), // 申込者コード
          "applicant_name": request.string('applicant_name'), //申込者名
          "applicant_addr": request.string('applicant_addr'), //申込者住所
          "applicant_tel": request.string('applicant_tel'), //申込者電話番号
          "applicant_fax": request.string('applicant_fax'), //申込者FAX番号
          "applicant_mail": request.string('applicant_mail'), //申込者メールアドレス
          "uketsuke_date": request.string('uketsuke_date'), // 受付日付
          "name1": request.string('name1'), // 氏名1
          "name2": request.string('name2'), // 氏名2
          "name_kana": request.string('name_kana'), // 氏名カナ
          "old_or_common_name_type":
              request.string('old_or_common_name_type'), // 旧姓通称区分
          "name3": request.string('name3'), // 氏名3
          "birth_day": request.string('birth_day'), // 生年月日
          "zip_code": request.string('zip_code'), // 郵便番号
          "addr1": request.string('addr1'), // 現住所1
          "addr2": request.string('addr2'), // 現住所2
          "tel_no": request.string('tel_no'), // 電話番号
          "mail_addr": request.string('mail_addr'), // メールアドレス
          "unacquired_kosyu_code":
              request.string('unacquired_kosyu_code'), // 未取得講習コード
          "declaration_party_name":
              request.string('declaration_party_name'), // 申告団体名
          "declaration_date": request.string('declaration_date'), // 申告交付日付
          "soufu_kubun": request.string('soufu_kubun'), // 送付先区分
          "url": request.string('url'), // 入力フォームURL
          "image_photo": request.string('image_photo'), // 本人写真画像データ(Base64)
          "filename_photo": request.string('filename_photo'), //本人写真ファイル名
          "filetype_photo":
              request.string('filetype_photo'), //本人写真ファイル形式(MIMEType)
          "image_kakunin_omote":
              request.string('image_kakunin_omote'), // 本人確認(表)画像データ(Base64)
          "filename_kakunin_omote":
              request.string('filename_kakunin_omote'), //本人確認(表)ファイル名
          "filetype_kakunin_omote": request
              .string('filetype_kakunin_omote'), //本人確認(表)ファイル形式(MIMEType)
          "image_kakunin_ura":
              request.string('image_kakunin_ura'), // 本人確認(裏)画像データ(Base64)
          "filename_kakunin_ura":
              request.string('filename_kakunin_ura'), //本人確認(裏)ファイル名
          "filetype_kakunin_ura":
              request.string('filetype_kakunin_ura'), //本人確認(裏)ファイル形式(MIMEType)
          "image_kakunin_atsumi":
              request.string('image_kakunin_atsumi'), //本人確認(厚み)画像データ(Base64)
          "filename_kakunin_atsumi":
              request.string('filename_kakunin_atsumi'), //本人確認(厚み)ファイル名
          "filetype_kakunin_atsumi": request
              .string('filetype_kakunin_atsumi'), //本人確認(厚み)ファイル形式(MIMEType)
          "image_license1":
              request.string('image_license1'), //受講資格1画像データ(Base64)
          "filename_license1": request.string('filename_license1'), //受講資格1ファイル名
          "filetype_license1":
              request.string('filetype_license1'), //受講資格1ファイル形式(MIMEType)
          "image_license2":
              request.string('image_license2'), //受講資格2画像データ(Base64)
          "filename_license2": request.string('filename_license2'), //受講資格2ファイル名
          "filetype_license2":
              request.string('filetype_license2'), //受講資格2ファイル形式(MIMEType)
          "image_license3":
              request.string('image_license3'), //受講資格3画像データ(Base64)
          "filename_license3": request.string('filename_license3'), //受講資格3ファイル名
          "filetype_license3":
              request.string('filetype_license3'), //受講資格3ファイル形式(MIMEType)
        }
      }
    };

    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      final parsedJson = jsonDecode(response.body);
      // print(parsedJson);
      if (parsedJson["status"] == constants.API_RES_SUCCESS) {
        final extraAttributes = {
          //"uketsuke_id": parsedJson["responsedata"]["uketsuke_id"],
          "KEY#1":
              "${request.string('name_sei')} ${request.string('name_mei')}",
          "KEY#2": request.string('place'),
          "KEY#3": "URL",
          "KEY#4": request.string('place')
        };
        //then sent email
        return externalController.sendEmail(
          "",
          request.string('mail_addr'),
          "【再発行依頼】",
          constants.EMAIL_SAIHAKKOIRAI_DONE,
          extraAttributes,
          null,
        );
      }
      return Response.json(parsedJson);
    } else {
      throw Exception('Failed to load data');
    }
  }

  //再発行依頼ロード
  Future<Response> readSaihakkoIrai(Request request) async {
    request.validate({
      'uketsuke_id': 'required',
    }, {
      'uketsuke_id.required': '受付IDが必須です。',
    });

    final Map<String, dynamic> body = {
      "SaihakkoIraiApiManager": {
        "method": "read",
        "requestdata": {
          "uketsuke_id": request.input('uketsuke_id'),
        }
      }
    };
    // print(body);
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load data');
    }
  }

  //再発行依頼作成
  Future<Response> makeSaihakkoIrai(Request request) async {
    //prepare mail url params
    Map<String, String> mailUrlParam = {
      constants.STEPKEY: constants.STEP5,
    };
    final strUrl = Utils.makeUrl(env("APP_MAILURL"), null, mailUrlParam);

    final Map<String, dynamic> body = {
      "SaihakkoIraiApiManager": {
        "method": "write",
        "requestdata": {
          "uketsuke_id": "",
          "name_sei": request.string('name_sei'),
          "name_mei": request.string('name_mei'),
          "name_kana": request.string('name_kana'),
          "old_name": request.string('old_name'),
          "old_name_kana": request.string('old_name_kana'),
          "birth_day": request.string('birth_day'),
          "zip_code": request.string('zip_code'),
          "addr1": request.string('addr1'),
          "addr2": request.string('addr2'),
          "tel_no": request.string('tel_no'),
          "mail_addr": request.string('mail_addr'),
          "kosyu_type": request.string('kosyu_type'),
          "jimusyo_code": request.string('jimusyo_code'),
          "lost_flag": request.input('lost_flag'),
          "change_name_flag": request.input('change_name_flag'),
          "damage_flag": request.input('damage_flag'),
          "other_flag": request.input('other_flag'),
          "other_riyu": request.string('other_riyu'),
          "comment": request.string('comment'),
          "url": strUrl,
        }
      }
    };

    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      final parsedJson = jsonDecode(response.body);
      // print(parsedJson);
      if (parsedJson["status"] == constants.API_RES_SUCCESS) {
        final extraAttributes = {
          //"uketsuke_id": parsedJson["responsedata"]["uketsuke_id"],
          "KEY#1":
              "${request.string('name_sei')} ${request.string('name_mei')}",
          "KEY#2": parsedJson["responsedata"]["uketsuke_id"],
          "KEY#3": request.string('license'),
          "KEY#4": request.string('reason_title'),
          "KEY#5": request.string('place'),
          "KEY#6": request.string('comment')
        };
        //then sent email
        return externalController.sendEmail(
          "",
          request.string('mail_addr'),
          "【再発行依頼】",
          constants.EMAIL_SAIHAKKOIRAI,
          extraAttributes,
          null,
        );
      }
      print(parsedJson.toString());
      return Response.json(parsedJson);
    } else {
      throw Exception('Failed to load data');
    }
  }

  //再発行受付ロード
  Future<Response> readSaihakkoIraiUketsuke(Request request) async {
    request.validate({
      'uketsuke_id': 'required',
    }, {
      'uketsuke_id.required': '受付IDが必須です。',
    });

    final Map<String, dynamic> body = {
      "SaihakkoUketsukeApiManager": {
        "method": "read",
        "requestdata": {
          "uketsuke_id": request.string('uketsuke_id'),
        }
      }
    };
    // print(body);
    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to load uketsuke data');
    }
  }

  //再発行受付作成
  Future<Response> makeSaihakkoIraiUketsuke(Request request) async {
    //final newNumber = await dbController.getNewUketsukeId();
    // print(request.body.toString());
    // request.validate({
    //   'name_sei': 'required',
    //   'name_mei': 'required',
    // }, {
    //   'name_sei.required': '姓が必須です。',
    //   'name_mei.required': '名が必須です。',
    // });

    print(request.body.toString());

    final Map<String, dynamic> body = {
      "SaihakkoUketsukeApiManager": {
        "method": "write",
        "requestdata": request.body,
        // {
        //   "uketsuke_id": "",
        //   "name": request.string('name_sei') + request.string('name_mei'),
        //   "name_kana": request.string('name_kana'),
        //   "birth_day": request.string('birth_day'),
        //   "zip_code": request.string('zip_code'),
        //   "addr1": request.string('addr1'),
        //   "addr2": request.string('addr2'),
        //   "tel_no": request.string('tel_no'),
        //   "soufu_name": request.string('soufu_name'),
        //   "soufu_zip_code": request.string('soufu_zip_code'),
        //   "soufu_addr1": request.string('soufu_addr1'),
        //   "soufu_addr2": request.string('soufu_addr2'),
        //   "image_photo": request.string('image_photo'),
        //   "filename_photo": request.string('filename_photo'),
        //   "filetype_photo": request.string('filetype_photo'),
        //   "image_kakunin_omote": request.string('image_kakunin_omote'),
        //   "filename_kakunin_omote": request.string('filename_kakunin_omote'),
        //   "filetype_kakunin_omote": request.string('filetype_kakunin_omote'),
        //   "image_kakunin_ura": request.string('image_kakunin_ura'),
        //   "filename_kakunin_ura": request.string('filename_kakunin_ura'),
        //   "filetype_kakunin_ura": request.string('filetype_kakunin_ura'),
        //   "image_kakunin_atsumi": request.string('image_kakunin_atsumi'),
        //   "filename_kakunin_atsumi": request.string('filename_kakunin_atsumi'),
        //   "filetype_kakunin_atsumi": request.string('filetype_kakunin_atsumi'),
        //   "image_kakunin_rename": request.string('image_kakunin_rename'),
        //   "filename_kakunin_rename": request.string('filename_kakunin_rename'),
        //   "filetype_kakunin_rename": request.string('filetype_kakunin_rename'),
        //   "url": "", //request.string('url'),
        // }
      }
    };

    String jsonBody = json.encode(body);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      final parsedJson = jsonDecode(response.body);
      // print(parsedJson);
      // if (parsedJson["status"] == constants.API_RES_SUCCESS) {
      //   final extraAttributes = {
      //     //"uketsuke_id": parsedJson["responsedata"]["uketsuke_id"],
      //     "KEY#0":
      //         "${request.string('name_sei')} ${request.string('name_mei')}",
      //     "KEY#1": parsedJson["responsedata"]["uketsuke_id"],
      //     "KEY#2": request.string('license'),
      //     "KEY#3": request.string('reason_title'),
      //     "KEY#4": request.string('place')
      //   };
      //   //then sent email
      //   return externalController.sendEmail(
      //     "",
      //     request.string('mail_addr'),
      //     "【再発行依頼】",
      //     constants.EMAIL_SAIHAKKOIRAI,
      //     extraAttributes,
      //     null,
      //   );
      // }
      return Response.json(parsedJson);
    } else {
      throw Exception('Failed to load data');
    }
  }

  Future<Response> sendEmail(
    String from,
    String to,
    String subject,
    String templateId,
    Map<String, dynamic>? extraAttributes,
    Map<String, dynamic>? attachments,
  ) async {
    var emailTpl = await EmailTemplate()
        .query()
        .where('template_id', '=', templateId)
        .first();
    if (emailTpl == null) {
      return Response.json(
        {"message": "Cannot find the email template"},
        HttpStatus.badRequest,
      );
    }

    String emailTplStr = emailTpl["template"];
    String emailTplSubject = emailTpl["subject"];

    extraAttributes?.forEach((key, value) {
      emailTplStr = emailTplStr.replaceAll(key, value);
    });

    final Map<String, dynamic> body = {
      "SendMailApiManager": {
        "method": "sendmail",
        "requestdata": {
          "send_from": "", //from,
          "send_to": to,
          "subject": emailTplSubject, //subject,
          "body": emailTplStr,
          // "attached_file_data1": attachments[],
          // "attached_file_name1": "添付ファイル1名前",
          // "attached_file_data2": "添付ファイル2データ",
          // "attached_file_name2": "添付ファイル2名前",
          // "attached_file_data3": "添付ファイル3データ",
          // "attached_file_name3": "添付ファイル3名前"
        }
      }
    };

    String jsonBody = json.encode(body);

    print("email body:$jsonBody");
    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    if (response.statusCode == 200) {
      return Response.json(jsonDecode(response.body));
    } else {
      throw Exception('Failed to send email');
    }
  }

  Future<dynamic> makeUketsukeWithObj(obj) async {
    final Map<String, dynamic> body = {
      "UketsukeApiManager": {
        "method": "write",
        "requestdata": {
          "uketsuke_id": obj['uketsuke_id'], // 受付ID
          "kosyu_number": obj['kosyu_number'], // 講習番号
          "kosyu_code": obj['kosyu_code'], // 講習コード
          "kosyu_date": obj['kosyu_date'], // 講習日付
          "course_code": obj['course_code'], // コースコード
          "jimusyo_code": obj['jimusyo_code'], // 事務所コード
          "kaijyo_code": obj['kaijyo_code'], // 会場コード
          "applicant_number": obj['applicant_number'], // 申込番号
          "applicant_code": obj['applicant_code'], // 申込者コード
          "applicant_name": obj['applicant_name'], //申込者名
          "applicant_addr": obj['applicant_addr'], //申込者住所
          "applicant_tel": obj['applicant_tel'], //申込者電話番号
          "applicant_fax": obj['applicant_fax'], //申込者FAX番号
          "applicant_mail": obj['applicant_mail'], //申込者メールアドレス
          "uketsuke_date": obj['uketsuke_date'], // 受付日付
          "name1": obj['name1'], // 氏名1
          "name2": obj['name2'], // 氏名2
          "name_kana": obj['name_kana'], // 氏名カナ
          "old_or_common_name_type": obj['old_or_common_name_type'], // 旧姓通称区分
          "name3": obj['name3'], // 氏名3
          "birth_day": obj['birth_day'], // 生年月日
          "zip_code": obj['zip_code'], // 郵便番号
          "addr1": obj['addr1'], // 現住所1
          "addr2": obj['addr2'], // 現住所2
          "tel_no": obj['tel_no'], // 電話番号
          "mail_addr": obj['mail_addr'], // メールアドレス
          "sinkoku_jimusyo_code": obj["sinkoku_jimusyo_code"], // 申告事務所コード
          "sinkoku_kosyu_kubun": obj["sinkoku_kosyu_kubun"], // 申告講習区分
          "sinkoku_kosyu_date": obj["sinkoku_kosyu_date"], // 申告講習日付
          "sinkoku_kuwari_kubun": obj["sinkoku_kuwari_kubun"], // 申告区割区分
          "unacquired_kosyu_code": obj["unacquired_kosyu_code"], // 未取得講習コード
          "declaration_party_name": obj['declaration_party_name'], // 申告団体名
          "declaration_date": obj['declaration_date'], // 申告交付日付
          "soufu_kubun": obj['soufu_kubun'], // 送付先区分
          "url": obj['url'], // 入力フォームURL
          "image_photo": obj['image_photo'], // 本人写真画像データ(Base64)
          "filename_photo": obj['filename_photo'], //本人写真ファイル名
          "filetype_photo": obj['filetype_photo'], //本人写真ファイル形式(MIMEType)
          "image_kakunin_omote":
              obj['image_kakunin_omote'], // 本人確認(表)画像データ(Base64)
          "filename_kakunin_omote":
              obj['filename_kakunin_omote'], //本人確認(表)ファイル名
          "filetype_kakunin_omote":
              obj['filetype_kakunin_omote'], //本人確認(表)ファイル形式(MIMEType)
          "image_kakunin_ura": obj['image_kakunin_ura'], // 本人確認(裏)画像データ(Base64)
          "filename_kakunin_ura": obj['filename_kakunin_ura'], //本人確認(裏)ファイル名
          "filetype_kakunin_ura":
              obj['filetype_kakunin_ura'], //本人確認(裏)ファイル形式(MIMEType)
          "image_kakunin_atsumi":
              obj['image_kakunin_atsumi'], //本人確認(厚み)画像データ(Base64)
          "filename_kakunin_atsumi":
              obj['filename_kakunin_atsumi'], //本人確認(厚み)ファイル名
          "filetype_kakunin_atsumi":
              obj['filetype_kakunin_atsumi'], //本人確認(厚み)ファイル形式(MIMEType)
          "image_license1": obj['image_license1'], //受講資格1画像データ(Base64)
          "filename_license1": obj['filename_license1'], //受講資格1ファイル名
          "filetype_license1": obj['filetype_license1'], //受講資格1ファイル形式(MIMEType)
          "image_license2": obj['image_license2'], //受講資格2画像データ(Base64)
          "filename_license2": obj['filename_license2'], //受講資格2ファイル名
          "filetype_license2": obj['filetype_license2'], //受講資格2ファイル形式(MIMEType)
          "image_license3": obj['image_license3'], //受講資格3画像データ(Base64)
          "filename_license3": obj['filename_license3'], //受講資格3ファイル名
          "filetype_license3": obj['filetype_license3'], //受講資格3ファイル形式(MIMEType)
        }
      }
    };

    String jsonBody = json.encode(body);
    //REQUEST JSON BODY
    print("----- REQUEST JSON BODY -----");
    print(jsonBody);

    final response = await http.post(
      Uri.parse(env('APP_HONENDPOINT')),
      headers: headers,
      body: jsonBody,
      encoding: Encoding.getByName("utf-8"),
    );

    print("----- RESPONSE JSON BODY -----");
    print(response.body);
    if (response.statusCode != 200) {
      throw Exception('Failed to load data makeUketsukeWithObj');
    }
    return jsonDecode(response.body);
  }
}

final ExternalController externalController = ExternalController();
