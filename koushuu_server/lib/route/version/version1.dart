import 'package:koushuu_server/app/http/controllers/external_controller.dart';
import 'package:koushuu_server/app/http/middleware/apiauth_middleware.dart';
import 'package:koushuu_server/app/http/middleware/authenticate.dart';
import 'package:vania/vania.dart';
import 'package:koushuu_server/app/http/controllers/home_controller.dart';

class Version1 extends Route {
  @override
  void register() {
    Router.basePrefix('api/v1');

    Router.get("/home", homeController.index);

    Router.post("/getaddress", externalController.getAddressWithPostalCode)
        .middleware([ApiauthMiddleware()]);
    Router.post("/getkaijolist", externalController.getKaijoList)
        .middleware([ApiauthMiddleware()]);
    Router.post("/getcategorylist", externalController.getCategoryList)
        .middleware([ApiauthMiddleware()]);
    Router.post("/getkoushuulist", externalController.searchKoushuu)
        .middleware([ApiauthMiddleware()]);
    Router.post("/getpreflist", externalController.getPrefList)
        .middleware([ApiauthMiddleware()]);
    Router.post("/getkoushuuguide", externalController.getKoushuuGuide)
        .middleware([ApiauthMiddleware()]);
    Router.post("/makesaihakkoirai", externalController.makeSaihakkoIrai)
        .middleware([ApiauthMiddleware()]);
    Router.post("/readsaihakkoirai", externalController.readSaihakkoIrai)
        .middleware([ApiauthMiddleware()]);
    Router.post("/readsaihakkouketsuke",
            externalController.readSaihakkoIraiUketsuke)
        .middleware([ApiauthMiddleware()]);
    Router.post("/makesaihakkouketsuke",
            externalController.makeSaihakkoIraiUketsuke)
        .middleware([ApiauthMiddleware()]);

    Router.post("/koushuukubunlist", externalController.getKoushuuKubunList)
        .middleware([ApiauthMiddleware()]);
    Router.post("/koushuulist", externalController.getKoushuuList)
        .middleware([ApiauthMiddleware()]);
    Router.post("/koushuudatelist", externalController.getKoushuuDateList)
        .middleware([ApiauthMiddleware()]);
    Router.post("/koushuukuwarilist", externalController.getKoushuuKuwariList)
        .middleware([ApiauthMiddleware()]);

    Router.post("/openrequest", homeController.openRequest)
        .middleware([ApiauthMiddleware()]);
    Router.post("/closerequest", homeController.allDone)
        .middleware([ApiauthMiddleware()]);
    Router.post("/checkuser", homeController.checkUserExits)
        .middleware([ApiauthMiddleware()]);
    Router.post("/spass", homeController.setPassword)
        .middleware([ApiauthMiddleware()]);

    //Router.post("/register", authController.register);
    Router.post("/login", homeController.login);

    //Work data delete route
    Router.post("/wdd", homeController.deleteData);

    Router.group(() {
      Router.post("/participants", homeController.getParticipants);
      Router.post("/participant", homeController.getParticipant);
      Router.post("/rparticipant", homeController.crtParticipant);
      Router.post("/sparticipant", homeController.setParticipant);
    }, middleware: [
      //AuthenticateMiddleware(),
      ApiauthMiddleware(),
    ]);

    // Return Authenticated user data
    Router.get("/user", () {
      return Response.json(Auth().user());
    }).middleware([AuthenticateMiddleware()]);

    // Router.group(
    //   () {
    //     Router.get('all', postController.index);
    //     Router.post('create', postController.create);
    //   },
    //   prefix: 'post',
    //   middleware: [AuthenticateMiddleware()],
    // );
  }
}
