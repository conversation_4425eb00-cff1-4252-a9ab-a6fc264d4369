import 'dart:io';
import 'package:vania/vania.dart';
import 'create_user_table.dart';
import 'presonal_access_tokens_table.dart';
import 'email_templates_table.dart';
import 'create_settings_table.dart';
import 'applications_table.dart';
import 'members_table.dart';

void main(List<String> args) async {
  await MigrationConnection().setup();
  if (args.isNotEmpty && args.first.toLowerCase() == "migrate:fresh") {
    await Migrate().dropTables();
  } else {
    await Migrate().registry();
  }
  await MigrationConnection().closeConnection();
  exit(0);
}

class Migrate {
  registry() async {
    await CreateUserTable().up();
    await PresonalAccessTokensTable().up();
    await EmailTemplatesTable().up();
    await CreateSettingsTable().up();
    await ApplicationsTable().up();
    await MembersTable().up();
  }

  dropTables() async {
    await MembersTable().down();
    await ApplicationsTable().down();
    await CreateSettingsTable().down();
    await EmailTemplatesTable().down();
    await PresonalAccessTokensTable().down();
    await CreateUserTable().down();
  }
}
