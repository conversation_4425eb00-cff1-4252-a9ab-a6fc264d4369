import 'package:vania/vania.dart';

class ApplicationsTable extends Migration {
  @override
  Future<void> up() async {
    super.up();
    await createTableNotExists('applications', () {
      id();
      char(
        'applicant_id',
        length: 100,
      );
      char(
        'applicant_name',
        length: 100,
      );
      char(
        'applicant_tel',
        length: 100,
      );
      char(
        'applicant_fax',
        length: 100,
      );
      char(
        'applicant_addr',
        length: 100,
      );
      char(
        'email',
        length: 100,
      );
      integer(
        'people_cnt',
      );
      char(
        'kosyu_code',
        length: 200,
      );
      char(
        'kosyu_number',
        length: 200,
      );
      char(
        'kosyu_name',
        length: 200,
        defaultValue: "",
      );
      char(
        'kosyu_date',
        length: 200,
        defaultValue: "",
      );
      char(
        'jimusyo_code',
        length: 200,
        defaultValue: "",
      );
      char(
        'jimusyo_name',
        length: 200,
        defaultValue: "",
      );
      char(
        'course_code',
        length: 100,
        defaultValue: "",
      );
      char(
        'kaijo_name',
        length: 200,
        defaultValue: "",
      );
      integer(
        'status',
        defaultValue: 0,
      );
      timeStamp("deleted_at", defaultValue: null, nullable: true);
      timeStamps();
    });
  }

  @override
  Future<void> down() async {
    super.down();
    await dropIfExists('applications');
  }
}
