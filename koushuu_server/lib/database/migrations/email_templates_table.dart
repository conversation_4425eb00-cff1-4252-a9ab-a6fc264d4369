import 'package:vania/vania.dart';

class EmailTemplatesTable extends Migration {
  @override
  Future<void> up() async {
    super.up();
    await createTableNotExists('email_templates', () {
      id();
      char("template_id", length: 100, unique: true);
      char("subject", length: 255);
      longText("template");
      char("comment", length: 100, nullable: true);
      char("delete_flg", length: 1, defaultValue: "0");
      timeStamp('deleted_at', nullable: true);
      timeStamps();
    });
  }

  @override
  Future<void> down() async {
    super.down();
    await dropIfExists('email_templates');
  }
}
