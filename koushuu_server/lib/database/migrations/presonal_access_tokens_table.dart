import 'package:vania/vania.dart';

class PresonalAccessTokensTable extends Migration {
  @override
  Future<void> up() async {
    super.up();
    await createTableNotExists('presonal_access_tokens', () {
      id();
      char("name", length: 255);
      bigInt("tokenable_id", unique: true);
      char("token", length: 64, unique: true);
      timeStamps();
      dateTime("deleted_at");
    });
  }

  @override
  Future<void> down() async {
    super.down();
    await dropIfExists('presonal_access_tokens');
  }
}
