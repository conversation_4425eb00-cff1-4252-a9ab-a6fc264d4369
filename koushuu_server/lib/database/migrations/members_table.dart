import 'package:vania/vania.dart';

class MembersTable extends Migration {
  @override
  Future<void> up() async {
    super.up();
    await createTableNotExists('members', () {
      id();
      integer("application_id"); // 受付ID
      char("uketsuke_id", length: 20, defaultValue: ""); // 受付ID

      char("kosyu_number", length: 20); // 講習番号
      integer("kosyu_code"); // 講習コード
      char("kosyu_date", length: 12); // 講習日付
      integer("course_code"); // コースコード
      integer("jimusyo_code"); // 事務所コード
      integer("kaijyo_code"); // 会場コード
      char("applicant_number", length: 20); // 申込番号
      char("uketsuke_date", length: 12); // 受付日付
      integer("applicant_code", defaultValue: 0); // 申込者コード
      char("name1", length: 50, defaultValue: ""); // 氏名1
      char("name2", length: 50, defaultValue: ""); // 氏名2
      char("name_kana", length: 50, defaultValue: ""); // 氏名カナ
      integer("old_or_common_name_type",
          defaultValue: 0); // 旧姓通称区分(1:なし 2:旧姓 3:通称)
      char("name3", length: 50, defaultValue: ""); // 氏名3
      char("birth_day", length: 12, defaultValue: ""); // 生年月日
      char("zip_code", length: 10, defaultValue: ""); // 郵便番号
      char("addr1", length: 100, defaultValue: ""); // 現住所1
      char("addr2", length: 100, defaultValue: ""); // 現住所2
      char("tel_no", length: 100, defaultValue: ""); // 電話番号
      char("mail_addr", length: 159, defaultValue: ""); // メールアドレス

      // char("name_sei", length: 50); // 姓
      // char("name_mei", length: 50); // 名
      // char("name_kana", length: 100, nullable: true); // 氏名カナ
      // char("old_name", length: 50, nullable: true); // 旧姓
      // char("old_name_kana", length: 100, nullable: true); // 旧姓カナ
      // date("birth_day"); // 生年月日
      // char("zip_code", length: 10, nullable: true); // 郵便番号
      // char("addr1", length: 200, nullable: true); // 住所1
      // char("addr2", length: 200, nullable: true); // 住所2
      // char("tel_no", length: 15, nullable: true); // 電話番号
      // char("mail_addr", length: 100, nullable: true); // メールアドレス
      // char("kosyu_type", length: 50, nullable: true); // 講習種類
      // char("jimusyo_code", length: 6, nullable: true); // 事務所コード
      // integer("lost_flag", nullable: true); // 紛失フラグ
      // integer("change_name_flag", nullable: true); // 氏名変更フラグ
      // integer("damage_flag", nullable: true); // 破損汚れフラグ
      // integer("other_flag", nullable: true); // その他フラグ
      // char("other_riyu", length: 200, nullable: true); // その他理由
      // text("comment", nullable: true); // 連絡事項
      // char("soufu_name", length: 100, nullable: true); // 送付先名
      // char("soufu_zip_code", length: 10, nullable: true); // 送付先郵便番号
      // char("soufu_addr1", length: 200, nullable: true); // 送付先住所1
      // char("soufu_addr2", length: 200, nullable: true); // 送付先住所2

      integer("sinkoku_jimusyo_code", defaultValue: 0); // 申告事務所コード
      integer("sinkoku_kosyu_kubun", defaultValue: 0); // 申告講習区分
      char("sinkoku_kosyu_date", length: 10, defaultValue: ""); // 申告講習日付
      integer("sinkoku_kuwari_kubun", defaultValue: 0); // 申告区割区分
      integer("unacquired_kosyu_code", defaultValue: 0); // 未取得講習コード

      char("sinkoku_jimusyo_code_title",
          length: 500, nullable: true); // 申告事務所コード
      char("sinkoku_kosyu_kubun_title", length: 500, nullable: true); // 申告講習区分
      char("sinkoku_kuwari_kubun_title", length: 500, nullable: true); // 申告区割区分
      char("unacquired_kosyu_code_title",
          length: 500, nullable: true); // 未取得講習コード

      char("declaration_party_name", length: 200, nullable: true); // 申告団体名
      char("declaration_date", length: 12, nullable: true); // 申告交付日付
      integer("soufu_kubun", nullable: true); // 送付先区分(1:申込者 2:個人)
      char("url", length: 255, nullable: true); // 入力フォームアクセス用URL

      longText("image_photo", nullable: true); // 本人写真画像データ (Base64)
      char("filename_photo", length: 255, nullable: true); // 本人写真ファイル名
      char("filetype_photo",
          length: 255, nullable: true); // 本人写真ファイル形式(MIMEType)
      longText("image_kakunin_omote", nullable: true); // 本人確認(表)画像データ (Base64)
      char("filename_kakunin_omote",
          length: 255, nullable: true); // 本人確認(表)ファイル名
      char("filetype_kakunin_omote",
          length: 255, nullable: true); // 本人確認(表)ファイル形式(MIMEType)
      longText("image_kakunin_ura", nullable: true); // 本人確認(裏)画像データ (Base64)
      char("filename_kakunin_ura", length: 255, nullable: true); // 本人確認(裏)ファイル名
      char("filetype_kakunin_ura",
          length: 255, nullable: true); // 本人確認(裏)ファイル形式(MIMEType)
      longText("image_kakunin_atsumi",
          nullable: true); // 本人確認(厚み)画像データ (Base64)
      char("filename_kakunin_atsumi",
          length: 255, nullable: true); // 本人確認(厚み)ファイル名
      char("filetype_kakunin_atsumi",
          length: 255, nullable: true); // 本人確認(厚み)ファイル名(MIMEType)
      longText("image_license1", nullable: true); // 受講資格1画像データ(Base64)
      char("filename_license1", length: 255, nullable: true); // 受講資格1ファイル名
      char("filetype_license1",
          length: 255, nullable: true); // 受講資格1ファイル形式(MIMEType)
      longText("image_license2", nullable: true); // 受講資格2画像データ(Base64)
      char("filename_license2", length: 255, nullable: true); // 受講資格2ファイル名
      char("filetype_license2",
          length: 255, nullable: true); // 受講資格2ファイル形式(MIMEType)
      longText("image_license3", nullable: true); // 受講資格3画像データ(Base64)
      char("filename_license3", length: 255, nullable: true); // 受講資格3ファイル名
      char("filetype_license3",
          length: 255, nullable: true); // 受講資格3ファイル形式(MIMEType
      integer("status", defaultValue: 0); // 状態
      timeStamp('deleted_at', nullable: true); //削除
      timeStamps();
    });
  }

  @override
  Future<void> down() async {
    super.down();
    await dropIfExists('members');
  }
}
