import 'package:vania/vania.dart';

class CreateUserTable extends Migration {
  @override
  Future<void> up() async {
    super.up();
    await createTableNotExists('users', () {
      id();
      integer("application_id", nullable: true);
      char("name", length: 100, nullable: true);
      char("email", length: 191, unique: true);
      char("password", length: 200, defaultValue: null, nullable: true);
      timeStamps();
    });
  }

  @override
  Future<void> down() async {
    super.down();
    await dropIfExists('users');
  }
}
