services:
  # db:
  #   container_name: mysql8
  #   image: mysql:8.0
  #   command: mysqld --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
  #   environment:
  #     MYSQL_ROOT_PASSWORD: root
  #     #MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
  #     MYSQL_DATABASE: boiraweb
  #     MYSQL_USER: boira
  #     MYSQL_PASSWORD: Passw0rd
  #   ports:
  #     - 3306:3306
  #   expose:
  #     - 3306
  #   restart: always
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   healthcheck:
  #     test: ["CMD", "mysqladmin", "ping", "-h", "127.0.0.1"]
  #     timeout: 20s
  #     retries: 10
  vania:
    build: .
    image: app:latest
    container_name: app
    restart: always
    #working_dir: /app
    # platform: linux/amd64
    platform: linux/amd64v8 
    volumes:
      - app:/app
      #- ./:/app
      - ./public:/public
      - ./storage:/storage
    ports:
      - 8080:8080
    expose:
      - 8080
    # depends_on:
    #   db:
    #     condition: service_healthy
volumes:
  app:
  # mysql_data: