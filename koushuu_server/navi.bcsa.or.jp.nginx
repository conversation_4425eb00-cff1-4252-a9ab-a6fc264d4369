geo $bypass_maintenance {
	# default return maintenance page
	default         0;
	
	# allow this to access
	202.9.47.51	1;
	202.0.0.0/24	1;
	************	1;
}

server {
	listen 80;
	server_name navi.bcsa.or.jp;

	root /var/www/websystem;
	index index.html;

	location = /maintenance.html {
		internal;
		
		# if direct access, return 503
		error_page 404 =503 /50x.html;
		root /var/www/html/;
	}

	location / {
		error_page 503 /maintenance.html;
		if ($bypass_maintenance = 0) {
			return 503;
		}
		#try_files $uri $uri/ =404;
		if (!-e $request_filename){
			rewrite ^(.*)$ /index.html break;
		}
	}

	# Proxy requests under /api to localhost:8080
	location /api/ {
		rewrite ^/(.*)$ /$1 break;
		proxy_pass http://127.0.0.1:8080/api/;
		#proxy_http_version 1.1;    
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Proto $scheme;
    
    		# WebSocket対応
    		#proxy_set_header Upgrade $http_upgrade;
    		#proxy_set_header Connection "upgrade";
	}


	access_log /var/log/nginx/navi.bcsa.or.jp.access.log;
	error_log /var/log/nginx/navi.bcsa.or.jp.error.log;
}
