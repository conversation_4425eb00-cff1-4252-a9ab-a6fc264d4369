<?php

use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\MypageController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/
//Route::get('/', function () {
//    return view('welcome');
//});

Route::get('/order/{id?}', [OrderController::class, 'index'])
        ->name('order');
Route::get('/get_stor_products', [OrderController::class, 'getStorProducts']);
Route::post('/charge', [OrderController::class, 'charge']);
Route::post('/save', [OrderController::class, 'saveOrder']);
Route::post('/store', [OrderController::class, 'storeOrder']);
Route::get('/confirm', [OrderController::class, 'confirm']);
Route::get('/thanks', function () {
    return view('thanks');
});
Route::get('/error', function () {
    return view('error');
});


Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

Route::middleware('verified')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    Route::get('/mypage', [MypageController::class, 'index']);
    Route::get('/user_profile', [MypageController::class, 'userProfile']);
    Route::get('/get_user_profile', [MypageController::class, 'getUserProfile']);
    Route::post('/profile_update', [MypageController::class, 'userProfileUpdate']);
    Route::get('/shipping_address', [MypageController::class, 'shippingAddress']);
    Route::get('/get_shipping_address/{id}', [MypageController::class, 'getShippingAddress']);
    Route::post('/shipping_address_regist', [MypageController::class, 'registShippingAddress']);
    Route::post('/shipping_address_update', [MypageController::class, 'updateShippingAddress']);
    Route::get('/shipping_address_delete/{id}', [MypageController::class, 'deleteShippingAddress']);
});

require __DIR__.'/auth.php';
