import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:koushuu_system/repositories/request_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/footer_box.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:koushuu_system/core/constants.dart' as constants;
import 'package:koushuu_system/core/utils.dart';

class ReservationPage extends ConsumerWidget {
  ReservationPage({
    super.key,
    required this.title,
    required this.mancnt,
    required this.startDate,
    required this.endDate,
    required this.courseHour,
    required this.courseCode,
    required this.jimusyoCode,
    required this.jimusyoName,
    required this.price,
    required this.kaijo,
    required this.koushuuCode,
    required this.koushuuNumber,
  });

  final String title;
  final String kaijo;
  final int mancnt;
  final String startDate;
  final String endDate;
  final String courseHour;
  final String courseCode;
  final String jimusyoCode;
  final String jimusyoName;
  final String price;
  final String koushuuCode;
  final String koushuuNumber;

  final ScrollController _scrollController = ScrollController();

  final _formKey = GlobalKey<FormBuilderState>();

  final TextEditingController applicationIdController =
      TextEditingController(text: "0");
  final TextEditingController applicantNameController = TextEditingController();
  final TextEditingController telController = TextEditingController();
  final TextEditingController faxController = TextEditingController();
  final TextEditingController addrController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController reEmailController = TextEditingController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    var selectedPerson = '1';
    final lstCount = [for (var i = 1; i <= mancnt; i += 1) i];
    String formattedPrice = Utils.formatWithComma(int.parse(price));

    return Scaffold(
        body: FormBuilder(
          key: _formKey,
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Row(
                  children: [
                    TopBar(
                      title: title,
                      showCloseButton: true,
                    ),
                  ],
                ),
                Container(
                  margin: const EdgeInsets.all(10.0),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.black),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildRow('会場',
                          Text(kaijo, style: const TextStyle(fontSize: 16))),
                      _buildRow(
                          '日程',
                          Text('$startDate〜$endDate',
                              style: const TextStyle(fontSize: 16))),
                      _buildRow(
                          'コース',
                          Text(courseHour,
                              style: const TextStyle(fontSize: 16))),
                      _buildRow(
                          '料金',
                          Text('$formattedPrice円',
                              style: const TextStyle(fontSize: 16))),
                      _buildRow(
                        '予約人数',
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              FormBuilderDropdown<String>(
                                autofocus: true,
                                name: "person",
                                alignment: AlignmentDirectional.center,
                                decoration: const InputDecoration(
                                  contentPadding: EdgeInsets.symmetric(
                                    vertical: 0.0,
                                  ),
                                  border: OutlineInputBorder(),
                                  errorStyle: TextStyle(
                                      color: Colors.red,
                                      fontSize: 12), // エラー表示のスタイル
                                ),
                                initialValue: selectedPerson,
                                validator: (value) {
                                  if (value == null ||
                                      value.isEmpty ||
                                      int.parse(value) == 0) {
                                    return '予約人数を選択してください。';
                                  }
                                  return null;
                                },
                                onChanged: (String? newValue) {
                                  selectedPerson = newValue!;
                                },
                                items: lstCount.map((int num) {
                                  return DropdownMenuItem<String>(
                                    value: num.toString(),
                                    child: Text(num.toString()),
                                  );
                                }).toList(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 10),

                // Requirements Section
                Container(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '受講要件',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      _buildBulletPoint('クレーン・デリック運転士免許を有する方'),
                      _buildBulletPoint('床上操作式クレーン運転技能講習を修了された方'),
                      _buildBulletPoint('移動式クレーン運転士免許を有する方'),
                      _buildBulletPoint('玉掛け技能講習を修了された方'),
                      const SizedBox(height: 10),
                      const Text(
                        '必要書類',
                        style: TextStyle(
                            fontWeight: FontWeight.bold, fontSize: 16),
                      ),
                      _buildBulletPoint('受講申込書'),
                      _buildBulletPoint('受講申込書に記載の必要書類 A・B'),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                Container(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Form Section
                      // _buildTextField(
                      //     "appId", '申込書ID', applicationIdController, null),
                      _buildTextField(
                        "appName",
                        '申込者名',
                        applicantNameController,
                        FormBuilderValidators.compose([
                          FormBuilderValidators.required(),
                        ]),
                      ),
                      _buildNumberField(
                        "tel",
                        '電話番号',
                        telController,
                        FormBuilderValidators.compose(
                            [FormBuilderValidators.numeric()]),
                      ),
                      _buildTextField("addr", '住所', addrController, null),
                      _buildTextField(
                        "email",
                        'メールアドレス',
                        emailController,
                        FormBuilderValidators.compose([
                          FormBuilderValidators.required(),
                          FormBuilderValidators.email(),
                        ]),
                      ),
                      _buildTextField(
                        "reemail",
                        '再メールアドレス',
                        reEmailController,
                        FormBuilderValidators.compose([
                          FormBuilderValidators.required(),
                          FormBuilderValidators.email(),
                          (value) {
                            if (value != emailController.text) {
                              return 'メールアドレスが一致していません。';
                            }
                            return null;
                          },
                        ]),
                      ),
                      _buildNumberField(
                        "fax",
                        'FAX番号',
                        faxController,
                        FormBuilderValidators.compose(
                            [FormBuilderValidators.numeric()]),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                // Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CoreButton(
                      onPressed: () {
                        GoRouter.of(context).pop();
                      },
                      bgColor: Colors.grey,
                      fgColor: Colors.white,
                      title: 'キャンセル',
                    ),
                    const SizedBox(width: 20),
                    CoreButton(
                      onPressed: () async {
                        // Form is valid
                        if (_formKey.currentState?.validate() ?? false) {
                          context.loaderOverlay.show();
                          Map<String, dynamic> extra = <String, dynamic>{
                            "KEY#1": applicationIdController.text,
                            "KEY#2": applicantNameController.text,
                            "KEY#3": kaijo,
                            "KEY#4": courseHour,
                            "KEY#5": kaijo,
                            "KEY#6": '$startDate〜$endDate',
                            "KEY#7": courseHour,
                            //"KEY#7": '$price円',
                            "KEY#8": selectedPerson.toString(),
                          };

                          Map<String, dynamic> objReq = <String, dynamic>{
                            "applicant_id": applicationIdController.text,
                            "applicant_name": applicantNameController.text,
                            "applicant_tel": telController.text,
                            "applicant_fax": faxController.text,
                            "applicant_addr": addrController.text,
                            "email": emailController.text,
                            "course_code": courseCode,
                            "course_name": courseHour,
                            "people_cnt": selectedPerson.toString(),
                            "extra_attributes": extra,
                            "kousyu_code": koushuuCode,
                            "kousyu_number": koushuuNumber,
                            "kousyu_name": courseHour,
                            "kousyu_date": '$startDate〜$endDate',
                            "jimusyo_code": jimusyoCode,
                            "jimusyo_name": jimusyoName,
                            "kaijo_name": kaijo,
                          };

                          final res = await ref
                              .read(requestRepositoryProvider)
                              .openRequest(objReq);

                          context.loaderOverlay.hide();

                          if (res["status"] == constants.API_RES_SUCCESS) {
                            GoRouter.of(context).go('/resultsent');
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('受講申請出来ませんでした！')),
                            );
                          }
                        }
                      },
                      bgColor: Colors.red,
                      fgColor: Colors.white,
                      title: '次に進む',
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                const Row(
                  children: [
                    FooterBox(),
                  ],
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}

Widget _buildRow(String title, Widget addonWidget) {
  return Container(
    decoration: BoxDecoration(border: Border.all(color: Colors.grey)),
    child: SizedBox(
      height: 48,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.center,
            height: 48,
            width: 100,
            color: Colors.grey,
            child: Text(title,
                style: const TextStyle(fontWeight: FontWeight.bold)),
          ),
          addonWidget
        ],
      ),
    ),
  );
}

Widget _buildBulletPoint(String text) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      const Text('・', style: TextStyle(fontSize: 16)),
      Expanded(child: Text(text)),
    ],
  );
}

Widget _buildTextField(
  String name,
  String label,
  TextEditingController controller,
  String? Function(String?)? validator,
) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 5.0),
    child: FormBuilderTextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
      validator: validator,
      name: name,
    ),
  );
}

Widget _buildNumberField(
  String name,
  String label,
  TextEditingController controller,
  String? Function(String?)? validator,
) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 5.0),
    child: FormBuilderTextField(
      controller: controller,
      keyboardType: TextInputType.number,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly, // 数字のみ
      ],
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
      validator: validator,
      name: name,
    ),
  );
}
