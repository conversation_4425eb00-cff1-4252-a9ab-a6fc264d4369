import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/models/participant.dart';
import 'package:koushuu_system/repositories/participant_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/field_title.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:koushuu_system/core/constants.dart' as constants;
import 'package:loader_overlay/loader_overlay.dart';

class CheckParticipantPage extends ConsumerWidget {
  CheckParticipantPage(
      {super.key,
      required this.title,
      required this.p,
      required this.flgView,
      this.flgOut = 0});

  final String title;
  final Participant? p;
  final int flgView;
  final int flgOut;
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(
                children: [
                  TopBar(
                    title: title,
                  ),
                ],
              ),
              const SizedBox(
                height: 10,
              ),
              const Padding(
                padding: EdgeInsets.only(left: 20.0, right: 20.0),
                child: Row(
                    // crossAxisAlignment: CrossAxisAlignment.stretch,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      DefaultText(
                        txt: '登録情報確認',
                      ),
                    ]),
              ),
              const SizedBox(
                height: 10,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          _buildTextField("name1", "名称1", p!.name1 ?? "", true),
                          _buildTextField("name2", "名称2", p!.name2 ?? "", true),
                          _buildTextField(
                              "furigana", "ふりがな", p!.name_kana ?? "", true),
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 20.0, right: 20, top: 20, bottom: 0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: FormBuilderRadioGroup<String>(
                                    enabled: false,
                                    wrapAlignment: WrapAlignment.start,
                                    name: 'optReason',
                                    initialValue:
                                        p!.old_or_common_name_type.toString(),
                                    options: const [
                                      FormBuilderFieldOption(
                                          value: '1', child: Text('なし')),
                                      FormBuilderFieldOption(
                                          value: '2', child: Text('旧姓')),
                                      FormBuilderFieldOption(
                                          value: '3', child: Text('通作')),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          _buildTextField(
                              "reason", "理由", p!.reason ?? "", true),
                          _buildTextField(
                              "birthday", "生年月日", p!.birth_day ?? "", true),
                          _buildTextField(
                              "postal", "〒", p!.zip_code ?? "", true),
                          _buildTextField("addr1", "住所１", p!.addr1 ?? "", true),
                          _buildTextField("addr2", "住所２", p!.addr2 ?? "", true),
                          _buildTextField(
                              "tel1", "TEL1", p!.tel_no ?? "", true),
                          _buildTextField(
                              "email", "メールアドレス", p!.mail_addr ?? "", true),
                          const Divider(),
                          _buildPhotoField(
                              "photo1", '本人写真', p!.image_photo ?? ""),
                          _buildPhotoField("photo2", '本人確認書類　表',
                              p!.image_kakunin_omote ?? ""),
                          _buildPhotoField(
                              "photo3", '本人確認書類　裏', p!.image_kakunin_ura ?? ""),
                          // _buildPhotoField("photo4", '本人確認書類　厚み',
                          //     p!.image_kakunin_atsumi ?? ""),
                          const Divider(),
                          _buildPhotoField(
                              "photo5", '資格１', p!.image_license1 ?? ""),
                          _buildPhotoField(
                              "photo6", '資格２', p!.image_license2 ?? ""),
                          _buildPhotoField(
                              "photo7", '資格３', p!.image_license3 ?? ""),
                          const Padding(
                            padding: EdgeInsets.only(
                                left: 20.0, right: 20, top: 20, bottom: 0),
                            child: DefaultText(
                              txt: "※これから取得予定の方は以下に申告入力してください",
                              fontSize: 16,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 20.0, right: 20, top: 20, bottom: 0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: FormBuilderRadioGroup<String>(
                                    wrapAlignment: WrapAlignment.start,
                                    enabled: false,
                                    name: 'optCompany',
                                    initialValue: p!.opt_company,
                                    options: const [
                                      FormBuilderFieldOption(
                                          value: '1', child: Text('当協会')),
                                      FormBuilderFieldOption(
                                          value: '2', child: Text('他団体')),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const FieldTitle(
                            title: "↓ボイラクレーン安全協会の場合",
                            txtColor: Colors.red,
                          ),
                          _buildTextField("jimusho", "事務所",
                              p!.sinkoku_jimusyo_code_title ?? "", true),
                          _buildTextField("koushuukubun", "講習区分",
                              p!.sinkoku_kosyu_kubun_title ?? "", true),
                          _buildTextField("koushuu", "講習",
                              p!.unacquired_kosyu_code_title ?? "", true),
                          _buildTextField("koushuudate", "受講日",
                              p!.sinkoku_kosyu_date ?? "", true),
                          _buildTextField("kuwari", "区割区分",
                              p!.sinkoku_kuwari_kubun_title ?? "", true),
                          const SizedBox(
                            height: 20,
                          ),
                          const Divider(),
                          const SizedBox(
                            height: 20,
                          ),
                          const FieldTitle(
                            title: "↓他団体の場合",
                            txtColor: Colors.red,
                          ),
                          _buildTextField("dantai", "団体名",
                              p!.declaration_party_name ?? "", true),
                          _buildTextField("enddate", "交付日/修了日",
                              p!.declaration_date ?? "", true),
                          const SizedBox(
                            height: 20,
                          ),
                          const Divider(),
                          const SizedBox(
                            height: 20,
                          ),
                          const FieldTitle(
                            title: "修了証送付先",
                            txtColor: Colors.red,
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 20.0, right: 20, top: 20, bottom: 0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: FormBuilderRadioGroup<String>(
                                    enabled: false,
                                    wrapAlignment: WrapAlignment.start,
                                    name: 'optSofu',
                                    initialValue: p!.soufu_kubun.toString(),
                                    options: const [
                                      FormBuilderFieldOption(
                                          value: '1', child: Text('申込会社')),
                                      FormBuilderFieldOption(
                                          value: '2', child: Text('自宅')),
                                    ],
                                    separator: null, // 初期選択
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              Padding(
                padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CoreButton(
                      onPressed: () {
                        GoRouter.of(context).pop();
                      },
                      bgColor: Colors.red,
                      title: "入力に戻る",
                      fontSize: 20,
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    (flgView == 0 && flgOut == 1)
                        ? CoreButton(
                            onPressed: () async {
                              if (p != null) {
                                context.loaderOverlay.show();
                                final reqJson = p!.toJson();
                                final res = await ref
                                    .read(participantRepositoryProvider)
                                    .newParticipant(reqJson);

                                context.loaderOverlay.hide();
                                if (res["status"] ==
                                    constants.API_RES_SUCCESS) {
                                  GoRouter.of(context)
                                      .pushReplacement("/eparticipant", extra: {
                                    "showParticipantsButton": 1,
                                  });
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text("情報登録できませんでした！"),
                                    ),
                                  );
                                }
                              }
                            },
                            bgColor: Colors.red,
                            title: "入力完了",
                            fontSize: 20,
                          )
                        : Container(),
                  ],
                ),
              ),
              const SizedBox(
                height: 40,
              ),
            ],
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }

  Widget _buildTextField(
    String name,
    String label,
    String value,
    bool isReadonly,
  ) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: FormBuilderTextField(
        name: name,
        initialValue: value,
        readOnly: isReadonly,
        decoration: InputDecoration(
          filled: true,
          fillColor: Colors.amber[50],
          //labelText: label,
          border: const OutlineInputBorder(),
        ),
      ),
    );
  }

  Widget _buildPhotoField(String fieldName, String lbl, String base64Image) {
    if (base64Image.contains(',')) {
      base64Image = base64Image.split(',')[1];
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: base64Image == ""
          ? const Text(
              "画像情報ありません！",
              style: TextStyle(color: Colors.red),
            )
          : Column(
              children: [
                Text(lbl),
                Image.memory(
                  base64Decode(base64Image),
                  fit: BoxFit.contain,
                ),
              ],
            ),
    );
  }
}
