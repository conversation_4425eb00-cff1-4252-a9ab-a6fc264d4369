import 'package:flutter/material.dart';

class ConditionModal extends StatelessWidget {
  const ConditionModal({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Material(
          clipBehavior: Clip.antiAlias,
          borderRadius: BorderRadius.circular(12),
          child: const Text("adsfasdfsdaf"),
        ),
      ),
    );
  }
}
