import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:koushuu_system/repositories/course_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/course_info.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/footer_box.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';

class KoushouDescriptionPage extends HookConsumerWidget {
  KoushouDescriptionPage(
      {super.key, required this.title, required this.koushuu_code});

  final String title;
  final String koushuu_code;
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fetchAsync = ref.watch(fetchCourseGuideProvider(koushuu_code));

    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Row(
                children: [
                  TopBar(
                    title: title,
                    showCloseButton: true,
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 20,
                          ),
                          const DefaultText(
                            txt: "取得済みの資格によって免除されるコースがあります、受講者情報入力時に免除資格を選択",
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          fetchAsync.when(
                            data: (val) {
                              if (val.isEmpty) {
                                return const Center(
                                  child: DefaultText(
                                    txt: "ご指定講習の説明が見つかりませんでした。",
                                    fontSize: 16,
                                  ),
                                );
                              } else {
                                return Column(
                                    children: val
                                        .map(
                                          (it) => CourseInfoCard(
                                            title: 'コース区分',
                                            subtitle: it.course_name,
                                            description: it.hold_licence,
                                          ),
                                        )
                                        .toList());
                              }
                            },
                            loading: () => const Center(
                              child: CircularProgressIndicator(),
                            ),
                            error: (error, _) => Text('error: $error'),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const Row(
                children: [
                  FooterBox(),
                ],
              ),
            ],
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}
