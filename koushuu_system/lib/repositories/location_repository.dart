import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/env_config.dart';
import 'package:koushuu_system/extensions/extensions.dart';
import 'package:koushuu_system/models/location.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'location_repository.g.dart';

class LocationRepository {
  Future<List<Location>> fetchLocation() async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/getkaijolist";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {},
      // queryParameters: {
      //   'param1': 'value1',
      // },
    );

    //debugPrint(postResponse.data);
    //debugPrint(postResponse.data.responsedata);
    var jsonData = json.decode(postResponse.toString());

    // debugPrint("-------dddddd-------");
    // debugPrint(jsonData.toString());
    // debugPrint("-------eeeeee-------");
    // debugPrint(jsonData["responsedata"]["kaijyo_list"].toString());
    // debugPrint("-------cccccc-------");

    Iterable l = jsonData["responsedata"]["kaijyo_list"];
    List<Location> locations =
        List<Location>.from(l.map((model) => Location.fromJson(model)));

    final result = locations
        .whereWithIndex((element, index) =>
            locations.indexWhere(
                (element2) => element2.kaijyo_code == element.kaijyo_code) ==
            index)
        .toList();

    return result;
  }
}

// this will generate a LocationRepositoryProvider
@riverpod
LocationRepository locationRepository(Ref ref) {
  return LocationRepository();
}

// this will generate a fetchWeatherProvider
@riverpod
Future<List<Location>> fetchLocation(Ref ref) {
  return ref.watch(locationRepositoryProvider).fetchLocation();
}
