import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/constants.dart' as constants;
import 'package:koushuu_system/core/env_config.dart';
import 'package:koushuu_system/models/address.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'address_repository.g.dart';

class AddressRepository {
  Future<Address> fetchData(postalCode) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/getaddress";

    // final defHeader = {
    //   'Content-Type': 'application/json',
    //   'authorization': 'token ${EnvConfig.APIKEY}',
    // };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        'zip_code': postalCode,
      },
    );
    var jsonData = json.decode(postResponse.toString());

    if (jsonData["status"] == constants.API_RES_FAILED) {
      return const Address(pref: "", city: "", area: "");
    }

    return Address.fromJson(jsonData["responsedata"]);
  }
}

// this will generate a AddressRepositoryProvider
@riverpod
AddressRepository addressRepository(Ref ref) {
  return AddressRepository();
}

@riverpod
Future<Address> fetchAddress(Ref ref, String postalCode) {
  return ref.watch(addressRepositoryProvider).fetchData(postalCode);
}
