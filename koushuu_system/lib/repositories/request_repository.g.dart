// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$requestRepositoryHash() => r'dab98ea7d82051d5e674db3b6fc4ce9bb555c74a';

/// See also [requestRepository].
@ProviderFor(requestRepository)
final requestRepositoryProvider =
    AutoDisposeProvider<RequestRepository>.internal(
  requestRepository,
  name: r'requestRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$requestRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef RequestRepositoryRef = AutoDisposeProviderRef<RequestRepository>;
String _$readReRequestHash() => r'c6cd2d0cad8c5e9a6e5d133294a45ce25c43bcb6';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [readReRequest].
@ProviderFor(readReRequest)
const readReRequestProvider = ReadReRequestFamily();

/// See also [readReRequest].
class ReadReRequestFamily extends Family<AsyncValue<Map<String, dynamic>>> {
  /// See also [readReRequest].
  const ReadReRequestFamily();

  /// See also [readReRequest].
  ReadReRequestProvider call(
    String uketsukeid,
  ) {
    return ReadReRequestProvider(
      uketsukeid,
    );
  }

  @override
  ReadReRequestProvider getProviderOverride(
    covariant ReadReRequestProvider provider,
  ) {
    return call(
      provider.uketsukeid,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'readReRequestProvider';
}

/// See also [readReRequest].
class ReadReRequestProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// See also [readReRequest].
  ReadReRequestProvider(
    String uketsukeid,
  ) : this._internal(
          (ref) => readReRequest(
            ref as ReadReRequestRef,
            uketsukeid,
          ),
          from: readReRequestProvider,
          name: r'readReRequestProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$readReRequestHash,
          dependencies: ReadReRequestFamily._dependencies,
          allTransitiveDependencies:
              ReadReRequestFamily._allTransitiveDependencies,
          uketsukeid: uketsukeid,
        );

  ReadReRequestProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.uketsukeid,
  }) : super.internal();

  final String uketsukeid;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(ReadReRequestRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ReadReRequestProvider._internal(
        (ref) => create(ref as ReadReRequestRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        uketsukeid: uketsukeid,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _ReadReRequestProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ReadReRequestProvider && other.uketsukeid == uketsukeid;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, uketsukeid.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ReadReRequestRef on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `uketsukeid` of this provider.
  String get uketsukeid;
}

class _ReadReRequestProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with ReadReRequestRef {
  _ReadReRequestProviderElement(super.provider);

  @override
  String get uketsukeid => (origin as ReadReRequestProvider).uketsukeid;
}

String _$makeReRequestHash() => r'748413a51bd55e79fd173e42aceaf39fb4b93901';

/// See also [makeReRequest].
@ProviderFor(makeReRequest)
const makeReRequestProvider = MakeReRequestFamily();

/// See also [makeReRequest].
class MakeReRequestFamily extends Family<AsyncValue> {
  /// See also [makeReRequest].
  const MakeReRequestFamily();

  /// See also [makeReRequest].
  MakeReRequestProvider call(
    ReRequest req,
  ) {
    return MakeReRequestProvider(
      req,
    );
  }

  @override
  MakeReRequestProvider getProviderOverride(
    covariant MakeReRequestProvider provider,
  ) {
    return call(
      provider.req,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'makeReRequestProvider';
}

/// See also [makeReRequest].
class MakeReRequestProvider extends AutoDisposeFutureProvider<Object?> {
  /// See also [makeReRequest].
  MakeReRequestProvider(
    ReRequest req,
  ) : this._internal(
          (ref) => makeReRequest(
            ref as MakeReRequestRef,
            req,
          ),
          from: makeReRequestProvider,
          name: r'makeReRequestProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$makeReRequestHash,
          dependencies: MakeReRequestFamily._dependencies,
          allTransitiveDependencies:
              MakeReRequestFamily._allTransitiveDependencies,
          req: req,
        );

  MakeReRequestProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.req,
  }) : super.internal();

  final ReRequest req;

  @override
  Override overrideWith(
    FutureOr<Object?> Function(MakeReRequestRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: MakeReRequestProvider._internal(
        (ref) => create(ref as MakeReRequestRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        req: req,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Object?> createElement() {
    return _MakeReRequestProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MakeReRequestProvider && other.req == req;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, req.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MakeReRequestRef on AutoDisposeFutureProviderRef<Object?> {
  /// The parameter `req` of this provider.
  ReRequest get req;
}

class _MakeReRequestProviderElement
    extends AutoDisposeFutureProviderElement<Object?> with MakeReRequestRef {
  _MakeReRequestProviderElement(super.provider);

  @override
  ReRequest get req => (origin as MakeReRequestProvider).req;
}

String _$readReRequestUketsukeHash() =>
    r'3e0392d24a90e214538f276e46df1c1bf1b76a63';

/// See also [readReRequestUketsuke].
@ProviderFor(readReRequestUketsuke)
const readReRequestUketsukeProvider = ReadReRequestUketsukeFamily();

/// See also [readReRequestUketsuke].
class ReadReRequestUketsukeFamily
    extends Family<AsyncValue<Map<String, dynamic>>> {
  /// See also [readReRequestUketsuke].
  const ReadReRequestUketsukeFamily();

  /// See also [readReRequestUketsuke].
  ReadReRequestUketsukeProvider call(
    String uketsukeid,
  ) {
    return ReadReRequestUketsukeProvider(
      uketsukeid,
    );
  }

  @override
  ReadReRequestUketsukeProvider getProviderOverride(
    covariant ReadReRequestUketsukeProvider provider,
  ) {
    return call(
      provider.uketsukeid,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'readReRequestUketsukeProvider';
}

/// See also [readReRequestUketsuke].
class ReadReRequestUketsukeProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// See also [readReRequestUketsuke].
  ReadReRequestUketsukeProvider(
    String uketsukeid,
  ) : this._internal(
          (ref) => readReRequestUketsuke(
            ref as ReadReRequestUketsukeRef,
            uketsukeid,
          ),
          from: readReRequestUketsukeProvider,
          name: r'readReRequestUketsukeProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$readReRequestUketsukeHash,
          dependencies: ReadReRequestUketsukeFamily._dependencies,
          allTransitiveDependencies:
              ReadReRequestUketsukeFamily._allTransitiveDependencies,
          uketsukeid: uketsukeid,
        );

  ReadReRequestUketsukeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.uketsukeid,
  }) : super.internal();

  final String uketsukeid;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(ReadReRequestUketsukeRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ReadReRequestUketsukeProvider._internal(
        (ref) => create(ref as ReadReRequestUketsukeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        uketsukeid: uketsukeid,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _ReadReRequestUketsukeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ReadReRequestUketsukeProvider &&
        other.uketsukeid == uketsukeid;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, uketsukeid.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin ReadReRequestUketsukeRef
    on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `uketsukeid` of this provider.
  String get uketsukeid;
}

class _ReadReRequestUketsukeProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with ReadReRequestUketsukeRef {
  _ReadReRequestUketsukeProviderElement(super.provider);

  @override
  String get uketsukeid => (origin as ReadReRequestUketsukeProvider).uketsukeid;
}

String _$makeReRequestUketsukeHash() =>
    r'a2f8a6f9ddb4d6e2702f923f819a32257f7d30be';

/// See also [makeReRequestUketsuke].
@ProviderFor(makeReRequestUketsuke)
const makeReRequestUketsukeProvider = MakeReRequestUketsukeFamily();

/// See also [makeReRequestUketsuke].
class MakeReRequestUketsukeFamily extends Family<AsyncValue> {
  /// See also [makeReRequestUketsuke].
  const MakeReRequestUketsukeFamily();

  /// See also [makeReRequestUketsuke].
  MakeReRequestUketsukeProvider call(
    ReRequestDoc req,
  ) {
    return MakeReRequestUketsukeProvider(
      req,
    );
  }

  @override
  MakeReRequestUketsukeProvider getProviderOverride(
    covariant MakeReRequestUketsukeProvider provider,
  ) {
    return call(
      provider.req,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'makeReRequestUketsukeProvider';
}

/// See also [makeReRequestUketsuke].
class MakeReRequestUketsukeProvider extends AutoDisposeFutureProvider<Object?> {
  /// See also [makeReRequestUketsuke].
  MakeReRequestUketsukeProvider(
    ReRequestDoc req,
  ) : this._internal(
          (ref) => makeReRequestUketsuke(
            ref as MakeReRequestUketsukeRef,
            req,
          ),
          from: makeReRequestUketsukeProvider,
          name: r'makeReRequestUketsukeProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$makeReRequestUketsukeHash,
          dependencies: MakeReRequestUketsukeFamily._dependencies,
          allTransitiveDependencies:
              MakeReRequestUketsukeFamily._allTransitiveDependencies,
          req: req,
        );

  MakeReRequestUketsukeProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.req,
  }) : super.internal();

  final ReRequestDoc req;

  @override
  Override overrideWith(
    FutureOr<Object?> Function(MakeReRequestUketsukeRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: MakeReRequestUketsukeProvider._internal(
        (ref) => create(ref as MakeReRequestUketsukeRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        req: req,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Object?> createElement() {
    return _MakeReRequestUketsukeProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MakeReRequestUketsukeProvider && other.req == req;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, req.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MakeReRequestUketsukeRef on AutoDisposeFutureProviderRef<Object?> {
  /// The parameter `req` of this provider.
  ReRequestDoc get req;
}

class _MakeReRequestUketsukeProviderElement
    extends AutoDisposeFutureProviderElement<Object?>
    with MakeReRequestUketsukeRef {
  _MakeReRequestUketsukeProviderElement(super.provider);

  @override
  ReRequestDoc get req => (origin as MakeReRequestUketsukeProvider).req;
}

String _$closeRequestHash() => r'dc2d62156eae96a5e37011d8a8491b9b8dd33fa8';

/// See also [closeRequest].
@ProviderFor(closeRequest)
const closeRequestProvider = CloseRequestFamily();

/// See also [closeRequest].
class CloseRequestFamily extends Family<AsyncValue> {
  /// See also [closeRequest].
  const CloseRequestFamily();

  /// See also [closeRequest].
  CloseRequestProvider call(
    dynamic req,
  ) {
    return CloseRequestProvider(
      req,
    );
  }

  @override
  CloseRequestProvider getProviderOverride(
    covariant CloseRequestProvider provider,
  ) {
    return call(
      provider.req,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'closeRequestProvider';
}

/// See also [closeRequest].
class CloseRequestProvider extends AutoDisposeFutureProvider<Object?> {
  /// See also [closeRequest].
  CloseRequestProvider(
    dynamic req,
  ) : this._internal(
          (ref) => closeRequest(
            ref as CloseRequestRef,
            req,
          ),
          from: closeRequestProvider,
          name: r'closeRequestProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$closeRequestHash,
          dependencies: CloseRequestFamily._dependencies,
          allTransitiveDependencies:
              CloseRequestFamily._allTransitiveDependencies,
          req: req,
        );

  CloseRequestProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.req,
  }) : super.internal();

  final dynamic req;

  @override
  Override overrideWith(
    FutureOr<Object?> Function(CloseRequestRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CloseRequestProvider._internal(
        (ref) => create(ref as CloseRequestRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        req: req,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Object?> createElement() {
    return _CloseRequestProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CloseRequestProvider && other.req == req;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, req.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CloseRequestRef on AutoDisposeFutureProviderRef<Object?> {
  /// The parameter `req` of this provider.
  dynamic get req;
}

class _CloseRequestProviderElement
    extends AutoDisposeFutureProviderElement<Object?> with CloseRequestRef {
  _CloseRequestProviderElement(super.provider);

  @override
  dynamic get req => (origin as CloseRequestProvider).req;
}

String _$openRequestHash() => r'8068ec19f24be513e771d6a665050df75b7461d1';

/// See also [openRequest].
@ProviderFor(openRequest)
const openRequestProvider = OpenRequestFamily();

/// See also [openRequest].
class OpenRequestFamily extends Family<AsyncValue> {
  /// See also [openRequest].
  const OpenRequestFamily();

  /// See also [openRequest].
  OpenRequestProvider call(
    dynamic req,
  ) {
    return OpenRequestProvider(
      req,
    );
  }

  @override
  OpenRequestProvider getProviderOverride(
    covariant OpenRequestProvider provider,
  ) {
    return call(
      provider.req,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'openRequestProvider';
}

/// See also [openRequest].
class OpenRequestProvider extends AutoDisposeFutureProvider<Object?> {
  /// See also [openRequest].
  OpenRequestProvider(
    dynamic req,
  ) : this._internal(
          (ref) => openRequest(
            ref as OpenRequestRef,
            req,
          ),
          from: openRequestProvider,
          name: r'openRequestProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$openRequestHash,
          dependencies: OpenRequestFamily._dependencies,
          allTransitiveDependencies:
              OpenRequestFamily._allTransitiveDependencies,
          req: req,
        );

  OpenRequestProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.req,
  }) : super.internal();

  final dynamic req;

  @override
  Override overrideWith(
    FutureOr<Object?> Function(OpenRequestRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: OpenRequestProvider._internal(
        (ref) => create(ref as OpenRequestRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        req: req,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Object?> createElement() {
    return _OpenRequestProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is OpenRequestProvider && other.req == req;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, req.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin OpenRequestRef on AutoDisposeFutureProviderRef<Object?> {
  /// The parameter `req` of this provider.
  dynamic get req;
}

class _OpenRequestProviderElement
    extends AutoDisposeFutureProviderElement<Object?> with OpenRequestRef {
  _OpenRequestProviderElement(super.provider);

  @override
  dynamic get req => (origin as OpenRequestProvider).req;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
