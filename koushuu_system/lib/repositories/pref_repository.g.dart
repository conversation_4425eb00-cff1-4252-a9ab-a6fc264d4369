// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pref_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$prefRepositoryHash() => r'd35e3d64ce58e1cae33dbbcd50901874633654cf';

/// See also [prefRepository].
@ProviderFor(prefRepository)
final prefRepositoryProvider = AutoDisposeProvider<PrefRepository>.internal(
  prefRepository,
  name: r'prefRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$prefRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef PrefRepositoryRef = AutoDisposeProviderRef<PrefRepository>;
String _$fetchPrefHash() => r'54a8310cccfb695abc7ad4f59ad2912b389451ff';

/// See also [fetchPref].
@ProviderFor(fetchPref)
final fetchPrefProvider = AutoDisposeFutureProvider<List<Pref>>.internal(
  fetchPref,
  name: r'fetchPrefProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$fetchPrefHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchPrefRef = AutoDisposeFutureProviderRef<List<Pref>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
