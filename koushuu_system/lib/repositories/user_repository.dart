import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/env_config.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'user_repository.g.dart';

class UserRepository {
  Future<String> checkExists(String email) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/checkuser";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        'email': email,
      },
    );
    var jsonData = json.decode(postResponse.toString());
    return jsonData["pass"];
  }

  Future<Response> login(String email, String pass) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/login";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    return await apiService.postRequest(
      strPath,
      data: {
        'email': email,
        'password': pass,
      },
    );
  }

  Future<Response> setPassword(String email, String password) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/spass";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);
    return await apiService.postRequest(
      strPath,
      data: {
        'email': email,
        'password': password,
      },
    );

    // Response postResponse = await apiService.postRequest(
    //   strPath,
    //   data: {
    //     'email': email,
    //     'password': password,
    //   },
    // );
    // var jsonData = json.decode(postResponse.toString());
    // return jsonData;
  }
}

// this will generate a UserRepositoryProvider
@riverpod
UserRepository userRepository(Ref ref) {
  return UserRepository();
}

@riverpod
Future<dynamic> checkUser(Ref ref, String email) {
  return ref.watch(userRepositoryProvider).checkExists(email);
}

@riverpod
Future<dynamic> setPassword(Ref ref, String email, String pass) {
  return ref.watch(userRepositoryProvider).setPassword(email, pass);
}

@riverpod
Future<dynamic> login(Ref ref, String email, String pass) {
  return ref.watch(userRepositoryProvider).login(email, pass);
}
