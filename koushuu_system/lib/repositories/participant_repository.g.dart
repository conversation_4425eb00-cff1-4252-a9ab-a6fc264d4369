// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'participant_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$participantRepositoryHash() =>
    r'039d69b26bbdd1b2692113e67b0a05f5949017d6';

/// See also [participantRepository].
@ProviderFor(participantRepository)
final participantRepositoryProvider =
    AutoDisposeProvider<ParticipantRepository>.internal(
  participantRepository,
  name: r'participantRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$participantRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef ParticipantRepositoryRef
    = AutoDisposeProviderRef<ParticipantRepository>;
String _$getParticipantsHash() => r'4ccb9d1d8aa4b68ff46d241206d0039a1cfa0f4d';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [getParticipants].
@ProviderFor(getParticipants)
const getParticipantsProvider = GetParticipantsFamily();

/// See also [getParticipants].
class GetParticipantsFamily extends Family<AsyncValue<dynamic>> {
  /// See also [getParticipants].
  const GetParticipantsFamily();

  /// See also [getParticipants].
  GetParticipantsProvider call(
    String applicationId,
  ) {
    return GetParticipantsProvider(
      applicationId,
    );
  }

  @override
  GetParticipantsProvider getProviderOverride(
    covariant GetParticipantsProvider provider,
  ) {
    return call(
      provider.applicationId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'getParticipantsProvider';
}

/// See also [getParticipants].
class GetParticipantsProvider extends AutoDisposeFutureProvider<dynamic> {
  /// See also [getParticipants].
  GetParticipantsProvider(
    String applicationId,
  ) : this._internal(
          (ref) => getParticipants(
            ref as GetParticipantsRef,
            applicationId,
          ),
          from: getParticipantsProvider,
          name: r'getParticipantsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$getParticipantsHash,
          dependencies: GetParticipantsFamily._dependencies,
          allTransitiveDependencies:
              GetParticipantsFamily._allTransitiveDependencies,
          applicationId: applicationId,
        );

  GetParticipantsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.applicationId,
  }) : super.internal();

  final String applicationId;

  @override
  Override overrideWith(
    FutureOr<dynamic> Function(GetParticipantsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetParticipantsProvider._internal(
        (ref) => create(ref as GetParticipantsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        applicationId: applicationId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<dynamic> createElement() {
    return _GetParticipantsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GetParticipantsProvider &&
        other.applicationId == applicationId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, applicationId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GetParticipantsRef on AutoDisposeFutureProviderRef<dynamic> {
  /// The parameter `applicationId` of this provider.
  String get applicationId;
}

class _GetParticipantsProviderElement
    extends AutoDisposeFutureProviderElement<dynamic> with GetParticipantsRef {
  _GetParticipantsProviderElement(super.provider);

  @override
  String get applicationId => (origin as GetParticipantsProvider).applicationId;
}

String _$getParticipantHash() => r'40095fd1e6757b45f47875582c99bc41320b13cd';

/// See also [getParticipant].
@ProviderFor(getParticipant)
const getParticipantProvider = GetParticipantFamily();

/// See also [getParticipant].
class GetParticipantFamily extends Family<AsyncValue<dynamic>> {
  /// See also [getParticipant].
  const GetParticipantFamily();

  /// See also [getParticipant].
  GetParticipantProvider call(
    String? participantId,
    String? uketsukeId,
  ) {
    return GetParticipantProvider(
      participantId,
      uketsukeId,
    );
  }

  @override
  GetParticipantProvider getProviderOverride(
    covariant GetParticipantProvider provider,
  ) {
    return call(
      provider.participantId,
      provider.uketsukeId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'getParticipantProvider';
}

/// See also [getParticipant].
class GetParticipantProvider extends AutoDisposeFutureProvider<dynamic> {
  /// See also [getParticipant].
  GetParticipantProvider(
    String? participantId,
    String? uketsukeId,
  ) : this._internal(
          (ref) => getParticipant(
            ref as GetParticipantRef,
            participantId,
            uketsukeId,
          ),
          from: getParticipantProvider,
          name: r'getParticipantProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$getParticipantHash,
          dependencies: GetParticipantFamily._dependencies,
          allTransitiveDependencies:
              GetParticipantFamily._allTransitiveDependencies,
          participantId: participantId,
          uketsukeId: uketsukeId,
        );

  GetParticipantProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.participantId,
    required this.uketsukeId,
  }) : super.internal();

  final String? participantId;
  final String? uketsukeId;

  @override
  Override overrideWith(
    FutureOr<dynamic> Function(GetParticipantRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: GetParticipantProvider._internal(
        (ref) => create(ref as GetParticipantRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        participantId: participantId,
        uketsukeId: uketsukeId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<dynamic> createElement() {
    return _GetParticipantProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is GetParticipantProvider &&
        other.participantId == participantId &&
        other.uketsukeId == uketsukeId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, participantId.hashCode);
    hash = _SystemHash.combine(hash, uketsukeId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin GetParticipantRef on AutoDisposeFutureProviderRef<dynamic> {
  /// The parameter `participantId` of this provider.
  String? get participantId;

  /// The parameter `uketsukeId` of this provider.
  String? get uketsukeId;
}

class _GetParticipantProviderElement
    extends AutoDisposeFutureProviderElement<dynamic> with GetParticipantRef {
  _GetParticipantProviderElement(super.provider);

  @override
  String? get participantId => (origin as GetParticipantProvider).participantId;
  @override
  String? get uketsukeId => (origin as GetParticipantProvider).uketsukeId;
}

String _$makeParticipantHash() => r'89e6667aa8151226bc5e870d97925fa27a9fdc46';

/// See also [makeParticipant].
@ProviderFor(makeParticipant)
const makeParticipantProvider = MakeParticipantFamily();

/// See also [makeParticipant].
class MakeParticipantFamily extends Family<AsyncValue<dynamic>> {
  /// See also [makeParticipant].
  const MakeParticipantFamily();

  /// See also [makeParticipant].
  MakeParticipantProvider call(
    Map<String, dynamic> p,
  ) {
    return MakeParticipantProvider(
      p,
    );
  }

  @override
  MakeParticipantProvider getProviderOverride(
    covariant MakeParticipantProvider provider,
  ) {
    return call(
      provider.p,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'makeParticipantProvider';
}

/// See also [makeParticipant].
class MakeParticipantProvider extends AutoDisposeFutureProvider<dynamic> {
  /// See also [makeParticipant].
  MakeParticipantProvider(
    Map<String, dynamic> p,
  ) : this._internal(
          (ref) => makeParticipant(
            ref as MakeParticipantRef,
            p,
          ),
          from: makeParticipantProvider,
          name: r'makeParticipantProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$makeParticipantHash,
          dependencies: MakeParticipantFamily._dependencies,
          allTransitiveDependencies:
              MakeParticipantFamily._allTransitiveDependencies,
          p: p,
        );

  MakeParticipantProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.p,
  }) : super.internal();

  final Map<String, dynamic> p;

  @override
  Override overrideWith(
    FutureOr<dynamic> Function(MakeParticipantRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: MakeParticipantProvider._internal(
        (ref) => create(ref as MakeParticipantRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        p: p,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<dynamic> createElement() {
    return _MakeParticipantProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MakeParticipantProvider && other.p == p;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, p.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MakeParticipantRef on AutoDisposeFutureProviderRef<dynamic> {
  /// The parameter `p` of this provider.
  Map<String, dynamic> get p;
}

class _MakeParticipantProviderElement
    extends AutoDisposeFutureProviderElement<dynamic> with MakeParticipantRef {
  _MakeParticipantProviderElement(super.provider);

  @override
  Map<String, dynamic> get p => (origin as MakeParticipantProvider).p;
}

String _$setParticipantHash() => r'dc154ec6db8b20f612651270e2cd95b93f2b2346';

/// See also [setParticipant].
@ProviderFor(setParticipant)
const setParticipantProvider = SetParticipantFamily();

/// See also [setParticipant].
class SetParticipantFamily extends Family<AsyncValue<dynamic>> {
  /// See also [setParticipant].
  const SetParticipantFamily();

  /// See also [setParticipant].
  SetParticipantProvider call(
    Map<String, dynamic> p,
  ) {
    return SetParticipantProvider(
      p,
    );
  }

  @override
  SetParticipantProvider getProviderOverride(
    covariant SetParticipantProvider provider,
  ) {
    return call(
      provider.p,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'setParticipantProvider';
}

/// See also [setParticipant].
class SetParticipantProvider extends AutoDisposeFutureProvider<dynamic> {
  /// See also [setParticipant].
  SetParticipantProvider(
    Map<String, dynamic> p,
  ) : this._internal(
          (ref) => setParticipant(
            ref as SetParticipantRef,
            p,
          ),
          from: setParticipantProvider,
          name: r'setParticipantProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$setParticipantHash,
          dependencies: SetParticipantFamily._dependencies,
          allTransitiveDependencies:
              SetParticipantFamily._allTransitiveDependencies,
          p: p,
        );

  SetParticipantProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.p,
  }) : super.internal();

  final Map<String, dynamic> p;

  @override
  Override overrideWith(
    FutureOr<dynamic> Function(SetParticipantRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: SetParticipantProvider._internal(
        (ref) => create(ref as SetParticipantRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        p: p,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<dynamic> createElement() {
    return _SetParticipantProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is SetParticipantProvider && other.p == p;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, p.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin SetParticipantRef on AutoDisposeFutureProviderRef<dynamic> {
  /// The parameter `p` of this provider.
  Map<String, dynamic> get p;
}

class _SetParticipantProviderElement
    extends AutoDisposeFutureProviderElement<dynamic> with SetParticipantRef {
  _SetParticipantProviderElement(super.provider);

  @override
  Map<String, dynamic> get p => (origin as SetParticipantProvider).p;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
