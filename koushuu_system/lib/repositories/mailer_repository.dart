import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/env_config.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'mailer_repository.g.dart';

class MailerRepository {
  Future sendEmail(String to, String subject, String templateId,
      Map<String, dynamic>? extraAttributes) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/mlr";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        "to": to,
        "subject": subject,
        "templateId": templateId,
        "extraAttributes": extraAttributes,
      },
    );
    var jsonData = json.decode(postResponse.toString());
    return jsonData;
  }
}

// this will generate a MailerRepositoryProvider
@riverpod
MailerRepository mailerRepository(Ref ref) {
  return MailerRepository();
}

@riverpod
Future sendEmail(Ref ref, String to, String subject, String templateId,
    Map<String, dynamic> extraAttributes) {
  return ref
      .watch(mailerRepositoryProvider)
      .sendEmail(to, subject, templateId, extraAttributes);
}
