import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:koushuu_system/core/api_service.dart';
import 'package:koushuu_system/core/env_config.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:koushuu_system/core/constants.dart' as constants;

part 'jimusho_repository.g.dart';

class JimushoRepository {
  Future<List<Map<String, dynamic>>> fetchJimushoList() async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/getpreflist";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {},
    );

    var jsonData = json.decode(postResponse.toString());

    if (jsonData["status"] == constants.API_RES_SUCCESS) {
      Iterable l = jsonData["responsedata"]["pref_list"];
      List<Map<String, dynamic>> lst = l.map<Map<String, dynamic>>((model) {
        if (model is Map<String, dynamic>) {
          return model;
        } else {
          return {}; // 無効なデータの場合、空のMapを返す
        }
      }).toList();

      return lst;
    }

    return [];
  }

  Future<List<Map<String, dynamic>>> fetchKoushuuKubunList(
    String jimusyo_code,
  ) async {
    if (jimusyo_code == "") {
      return [];
    }
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/koushuukubunlist";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        "jimusyo_code": jimusyo_code,
      },
    );

    var jsonData = json.decode(postResponse.toString());

    if (jsonData["status"] == constants.API_RES_SUCCESS) {
      Iterable l = jsonData["responsedata"]["kosyu_kubun_list"];
      List<Map<String, dynamic>> lst = l.map<Map<String, dynamic>>((model) {
        if (model is Map<String, dynamic>) {
          return model;
        } else {
          return {};
        }
      }).toList();

      return lst;
    }

    return [];
  }

  Future<List<Map<String, dynamic>>> fetchKoushuuList(
    String jimusyo_code,
    String kosyu_kubun,
  ) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/koushuulist";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        "jimusyo_code": jimusyo_code,
        "kosyu_kubun": kosyu_kubun,
      },
    );

    var jsonData = json.decode(postResponse.toString());

    if (jsonData["status"] == constants.API_RES_SUCCESS) {
      Iterable l = jsonData["responsedata"]["kosyu_list"];
      List<Map<String, dynamic>> lst = l.map<Map<String, dynamic>>((model) {
        if (model is Map<String, dynamic>) {
          return model;
        } else {
          return {};
        }
      }).toList();
      return lst;
    }
    return [];
  }

  Future<List<Map<String, dynamic>>> fetchKoushuuDateList(
    String jimusyo_code,
    String kosyu_kubun,
    String kosyu_code,
  ) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/koushuudatelist";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        "jimusyo_code": jimusyo_code,
        "kosyu_kubun": kosyu_kubun,
        "kosyu_code": kosyu_code,
      },
    );

    var jsonData = json.decode(postResponse.toString());

    if (jsonData["status"] == constants.API_RES_SUCCESS) {
      Iterable l = jsonData["responsedata"]["kosyu_date_list"];
      List<Map<String, dynamic>> lst = l.map<Map<String, dynamic>>((model) {
        if (model is Map<String, dynamic>) {
          return model;
        } else {
          return {};
        }
      }).toList();

      return lst;
    }
    return [];
  }

  Future<List<Map<String, dynamic>>> fetchKoushuuKuwariList(
    String jimusyo_code,
    String kosyu_kubun,
    String kosyu_code,
    String kosyu_date,
  ) async {
    ApiService apiService = ApiService.instance;

    final String endPoint = EnvConfig.APIENDPOINT;
    final String strPath = "$endPoint/v1/koushuukuwarilist";

    final defHeader = {
      'Content-Type': 'application/json',
      'authorization': 'token ${EnvConfig.APIKEY}',
    };

    // Configure Dio
    apiService.configureDio(baseUrl: endPoint, defaultHeaders: defHeader);

    Response postResponse = await apiService.postRequest(
      strPath,
      data: {
        "jimusyo_code": jimusyo_code,
        "kosyu_kubun": kosyu_kubun,
        "kosyu_code": kosyu_code,
        "kosyu_date": kosyu_date,
      },
    );

    var jsonData = json.decode(postResponse.toString());

    if (jsonData["status"] == constants.API_RES_SUCCESS) {
      Iterable l = jsonData["responsedata"]["kuwari_list"];
      List<Map<String, dynamic>> lst = l.map<Map<String, dynamic>>((model) {
        if (model is Map<String, dynamic>) {
          return model;
        } else {
          return {};
        }
      }).toList();

      return lst;
    }

    return [];
  }
}

// this will generate a JimushoRepositoryProvider
@riverpod
JimushoRepository jimushoRepository(Ref ref) {
  return JimushoRepository();
}

@riverpod
Future<List<Map<String, dynamic>>> fetchJimushoList(Ref ref) {
  return ref.watch(jimushoRepositoryProvider).fetchJimushoList();
}

@riverpod
Future<List<Map<String, dynamic>>> fetchKoushuuKubunList(
  Ref ref,
  String jimusyo_code,
) {
  return ref
      .watch(jimushoRepositoryProvider)
      .fetchKoushuuKubunList(jimusyo_code);
}

@riverpod
Future<List<Map<String, dynamic>>> fetchKoushuuList(
  Ref ref,
  String jimusyo_code,
  String kosyu_kubun,
) {
  return ref.watch(jimushoRepositoryProvider).fetchKoushuuList(
        jimusyo_code,
        kosyu_kubun,
      );
}

@riverpod
Future<List<Map<String, dynamic>>> fetchKoushuuDateList(
  Ref ref,
  String jimusyo_code,
  String kosyu_kubun,
  String kosyu_code,
) {
  return ref.watch(jimushoRepositoryProvider).fetchKoushuuDateList(
        jimusyo_code,
        kosyu_kubun,
        kosyu_code,
      );
}

@riverpod
Future<List<Map<String, dynamic>>> fetchKoushuuKuwariList(
  Ref ref,
  String jimusyo_code,
  String kosyu_kubun,
  String kosyu_code,
  String kosyu_date,
) {
  return ref.watch(jimushoRepositoryProvider).fetchKoushuuKuwariList(
        jimusyo_code,
        kosyu_kubun,
        kosyu_code,
        kosyu_date,
      );
}
