// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'address_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addressRepositoryHash() => r'bbed69b760fc37b7998d1cc24e85ee323cc32ea3';

/// See also [addressRepository].
@ProviderFor(addressRepository)
final addressRepositoryProvider =
    AutoDisposeProvider<AddressRepository>.internal(
  addressRepository,
  name: r'addressRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$addressRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AddressRepositoryRef = AutoDisposeProviderRef<AddressRepository>;
String _$fetchAddressHash() => r'0acbba78c24e2ad11ab4131eeed61b7252af13a5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchAddress].
@ProviderFor(fetchAddress)
const fetchAddressProvider = FetchAddressFamily();

/// See also [fetchAddress].
class FetchAddressFamily extends Family<AsyncValue<Address>> {
  /// See also [fetchAddress].
  const FetchAddressFamily();

  /// See also [fetchAddress].
  FetchAddressProvider call(
    String postalCode,
  ) {
    return FetchAddressProvider(
      postalCode,
    );
  }

  @override
  FetchAddressProvider getProviderOverride(
    covariant FetchAddressProvider provider,
  ) {
    return call(
      provider.postalCode,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchAddressProvider';
}

/// See also [fetchAddress].
class FetchAddressProvider extends AutoDisposeFutureProvider<Address> {
  /// See also [fetchAddress].
  FetchAddressProvider(
    String postalCode,
  ) : this._internal(
          (ref) => fetchAddress(
            ref as FetchAddressRef,
            postalCode,
          ),
          from: fetchAddressProvider,
          name: r'fetchAddressProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchAddressHash,
          dependencies: FetchAddressFamily._dependencies,
          allTransitiveDependencies:
              FetchAddressFamily._allTransitiveDependencies,
          postalCode: postalCode,
        );

  FetchAddressProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.postalCode,
  }) : super.internal();

  final String postalCode;

  @override
  Override overrideWith(
    FutureOr<Address> Function(FetchAddressRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchAddressProvider._internal(
        (ref) => create(ref as FetchAddressRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        postalCode: postalCode,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Address> createElement() {
    return _FetchAddressProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchAddressProvider && other.postalCode == postalCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, postalCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchAddressRef on AutoDisposeFutureProviderRef<Address> {
  /// The parameter `postalCode` of this provider.
  String get postalCode;
}

class _FetchAddressProviderElement
    extends AutoDisposeFutureProviderElement<Address> with FetchAddressRef {
  _FetchAddressProviderElement(super.provider);

  @override
  String get postalCode => (origin as FetchAddressProvider).postalCode;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
