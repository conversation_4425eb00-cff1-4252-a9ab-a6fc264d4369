// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'participant.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ParticipantImpl _$$ParticipantImplFromJson(Map<String, dynamic> json) =>
    _$ParticipantImpl(
      id: (json['id'] as num?)?.toInt(),
      application_id: (json['application_id'] as num?)?.toInt(),
      uketsuke_id: json['uketsuke_id'] as String?,
      kosyu_number: json['kosyu_number'] as String?,
      kosyu_code: (json['kosyu_code'] as num?)?.toInt(),
      kosyu_date: json['kosyu_date'] as String?,
      course_code: (json['course_code'] as num?)?.toInt(),
      jimusyo_code: (json['jimusyo_code'] as num?)?.toInt(),
      kaijyo_code: (json['kaijyo_code'] as num?)?.toInt(),
      applicant_number: json['applicant_number'] as String?,
      uketsuke_date: json['uketsuke_date'] as String?,
      applicant_code: (json['applicant_code'] as num?)?.toInt(),
      name1: json['name1'] as String?,
      name2: json['name2'] as String?,
      name_kana: json['name_kana'] as String?,
      old_or_common_name_type:
          (json['old_or_common_name_type'] as num?)?.toInt(),
      reason: json['reason'] as String?,
      name3: json['name3'] as String?,
      birth_day: json['birth_day'] as String?,
      zip_code: json['zip_code'] as String?,
      addr1: json['addr1'] as String?,
      addr2: json['addr2'] as String?,
      tel_no: json['tel_no'] as String?,
      mail_addr: json['mail_addr'] as String?,
      opt_company: json['opt_company'] as String?,
      unacquired_kosyu_code: (json['unacquired_kosyu_code'] as num?)?.toInt(),
      declaration_party_name: json['declaration_party_name'] as String?,
      declaration_date: json['declaration_date'] as String?,
      soufu_kubun: (json['soufu_kubun'] as num?)?.toInt(),
      url: json['url'] as String?,
      image_photo: json['image_photo'] as String?,
      filename_photo: json['filename_photo'] as String?,
      filetype_photo: json['filetype_photo'] as String?,
      image_kakunin_omote: json['image_kakunin_omote'] as String?,
      filename_kakunin_omote: json['filename_kakunin_omote'] as String?,
      filetype_kakunin_omote: json['filetype_kakunin_omote'] as String?,
      image_kakunin_ura: json['image_kakunin_ura'] as String?,
      filename_kakunin_ura: json['filename_kakunin_ura'] as String?,
      filetype_kakunin_ura: json['filetype_kakunin_ura'] as String?,
      image_kakunin_atsumi: json['image_kakunin_atsumi'] as String?,
      filename_kakunin_atsumi: json['filename_kakunin_atsumi'] as String?,
      filetype_kakunin_atsumi: json['filetype_kakunin_atsumi'] as String?,
      image_license1: json['image_license1'] as String?,
      filename_license1: json['filename_license1'] as String?,
      filetype_license1: json['filetype_license1'] as String?,
      image_license2: json['image_license2'] as String?,
      filename_license2: json['filename_license2'] as String?,
      filetype_license2: json['filetype_license2'] as String?,
      image_license3: json['image_license3'] as String?,
      filename_license3: json['filename_license3'] as String?,
      filetype_license3: json['filetype_license3'] as String?,
      sinkoku_jimusyo_code: (json['sinkoku_jimusyo_code'] as num?)?.toInt(),
      sinkoku_kosyu_kubun: (json['sinkoku_kosyu_kubun'] as num?)?.toInt(),
      sinkoku_kosyu_date: json['sinkoku_kosyu_date'] as String?,
      sinkoku_kuwari_kubun: (json['sinkoku_kuwari_kubun'] as num?)?.toInt(),
      unacquired_kosyu_code_title:
          json['unacquired_kosyu_code_title'] as String?,
      sinkoku_jimusyo_code_title: json['sinkoku_jimusyo_code_title'] as String?,
      sinkoku_kosyu_kubun_title: json['sinkoku_kosyu_kubun_title'] as String?,
      sinkoku_kuwari_kubun_title: json['sinkoku_kuwari_kubun_title'] as String?,
    );

Map<String, dynamic> _$$ParticipantImplToJson(_$ParticipantImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'application_id': instance.application_id,
      'uketsuke_id': instance.uketsuke_id,
      'kosyu_number': instance.kosyu_number,
      'kosyu_code': instance.kosyu_code,
      'kosyu_date': instance.kosyu_date,
      'course_code': instance.course_code,
      'jimusyo_code': instance.jimusyo_code,
      'kaijyo_code': instance.kaijyo_code,
      'applicant_number': instance.applicant_number,
      'uketsuke_date': instance.uketsuke_date,
      'applicant_code': instance.applicant_code,
      'name1': instance.name1,
      'name2': instance.name2,
      'name_kana': instance.name_kana,
      'old_or_common_name_type': instance.old_or_common_name_type,
      'reason': instance.reason,
      'name3': instance.name3,
      'birth_day': instance.birth_day,
      'zip_code': instance.zip_code,
      'addr1': instance.addr1,
      'addr2': instance.addr2,
      'tel_no': instance.tel_no,
      'mail_addr': instance.mail_addr,
      'opt_company': instance.opt_company,
      'unacquired_kosyu_code': instance.unacquired_kosyu_code,
      'declaration_party_name': instance.declaration_party_name,
      'declaration_date': instance.declaration_date,
      'soufu_kubun': instance.soufu_kubun,
      'url': instance.url,
      'image_photo': instance.image_photo,
      'filename_photo': instance.filename_photo,
      'filetype_photo': instance.filetype_photo,
      'image_kakunin_omote': instance.image_kakunin_omote,
      'filename_kakunin_omote': instance.filename_kakunin_omote,
      'filetype_kakunin_omote': instance.filetype_kakunin_omote,
      'image_kakunin_ura': instance.image_kakunin_ura,
      'filename_kakunin_ura': instance.filename_kakunin_ura,
      'filetype_kakunin_ura': instance.filetype_kakunin_ura,
      'image_kakunin_atsumi': instance.image_kakunin_atsumi,
      'filename_kakunin_atsumi': instance.filename_kakunin_atsumi,
      'filetype_kakunin_atsumi': instance.filetype_kakunin_atsumi,
      'image_license1': instance.image_license1,
      'filename_license1': instance.filename_license1,
      'filetype_license1': instance.filetype_license1,
      'image_license2': instance.image_license2,
      'filename_license2': instance.filename_license2,
      'filetype_license2': instance.filetype_license2,
      'image_license3': instance.image_license3,
      'filename_license3': instance.filename_license3,
      'filetype_license3': instance.filetype_license3,
      'sinkoku_jimusyo_code': instance.sinkoku_jimusyo_code,
      'sinkoku_kosyu_kubun': instance.sinkoku_kosyu_kubun,
      'sinkoku_kosyu_date': instance.sinkoku_kosyu_date,
      'sinkoku_kuwari_kubun': instance.sinkoku_kuwari_kubun,
      'unacquired_kosyu_code_title': instance.unacquired_kosyu_code_title,
      'sinkoku_jimusyo_code_title': instance.sinkoku_jimusyo_code_title,
      'sinkoku_kosyu_kubun_title': instance.sinkoku_kosyu_kubun_title,
      'sinkoku_kuwari_kubun_title': instance.sinkoku_kuwari_kubun_title,
    };
