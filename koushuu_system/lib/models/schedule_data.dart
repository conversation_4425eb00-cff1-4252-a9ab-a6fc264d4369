// This file is "main.dart"
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:koushuu_system/models/course.dart';

part 'schedule_data.freezed.dart';
part 'schedule_data.g.dart';

@freezed
class ScheduleData with _$ScheduleData {
  const factory ScheduleData({
    required String month,
    required List<AttendDay> attend_days,
  }) = _ScheduleData;

  factory ScheduleData.fromJson(Map<String, Object?> json) =>
      _$ScheduleDataFromJson(json);
}
