// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Location _$LocationFromJson(Map<String, dynamic> json) {
  return _Location.fromJson(json);
}

/// @nodoc
mixin _$Location {
  String get kaijyo_code => throw _privateConstructorUsedError;
  String get kaijyo_name => throw _privateConstructorUsedError;
  String get kaijyo_kana => throw _privateConstructorUsedError;
  String get zip_code => throw _privateConstructorUsedError; // 郵便番号
  String get addr1 => throw _privateConstructorUsedError; // 住所1
  String get addr2 => throw _privateConstructorUsedError; // 住所2
  String get tel_no => throw _privateConstructorUsedError; // 電話番号
  String get fax_no => throw _privateConstructorUsedError; // FAX番号
  String get memo => throw _privateConstructorUsedError;

  /// Serializes this Location to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LocationCopyWith<Location> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LocationCopyWith<$Res> {
  factory $LocationCopyWith(Location value, $Res Function(Location) then) =
      _$LocationCopyWithImpl<$Res, Location>;
  @useResult
  $Res call(
      {String kaijyo_code,
      String kaijyo_name,
      String kaijyo_kana,
      String zip_code,
      String addr1,
      String addr2,
      String tel_no,
      String fax_no,
      String memo});
}

/// @nodoc
class _$LocationCopyWithImpl<$Res, $Val extends Location>
    implements $LocationCopyWith<$Res> {
  _$LocationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? kaijyo_code = null,
    Object? kaijyo_name = null,
    Object? kaijyo_kana = null,
    Object? zip_code = null,
    Object? addr1 = null,
    Object? addr2 = null,
    Object? tel_no = null,
    Object? fax_no = null,
    Object? memo = null,
  }) {
    return _then(_value.copyWith(
      kaijyo_code: null == kaijyo_code
          ? _value.kaijyo_code
          : kaijyo_code // ignore: cast_nullable_to_non_nullable
              as String,
      kaijyo_name: null == kaijyo_name
          ? _value.kaijyo_name
          : kaijyo_name // ignore: cast_nullable_to_non_nullable
              as String,
      kaijyo_kana: null == kaijyo_kana
          ? _value.kaijyo_kana
          : kaijyo_kana // ignore: cast_nullable_to_non_nullable
              as String,
      zip_code: null == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String,
      addr1: null == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String,
      addr2: null == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String,
      tel_no: null == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String,
      fax_no: null == fax_no
          ? _value.fax_no
          : fax_no // ignore: cast_nullable_to_non_nullable
              as String,
      memo: null == memo
          ? _value.memo
          : memo // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LocationImplCopyWith<$Res>
    implements $LocationCopyWith<$Res> {
  factory _$$LocationImplCopyWith(
          _$LocationImpl value, $Res Function(_$LocationImpl) then) =
      __$$LocationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String kaijyo_code,
      String kaijyo_name,
      String kaijyo_kana,
      String zip_code,
      String addr1,
      String addr2,
      String tel_no,
      String fax_no,
      String memo});
}

/// @nodoc
class __$$LocationImplCopyWithImpl<$Res>
    extends _$LocationCopyWithImpl<$Res, _$LocationImpl>
    implements _$$LocationImplCopyWith<$Res> {
  __$$LocationImplCopyWithImpl(
      _$LocationImpl _value, $Res Function(_$LocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? kaijyo_code = null,
    Object? kaijyo_name = null,
    Object? kaijyo_kana = null,
    Object? zip_code = null,
    Object? addr1 = null,
    Object? addr2 = null,
    Object? tel_no = null,
    Object? fax_no = null,
    Object? memo = null,
  }) {
    return _then(_$LocationImpl(
      kaijyo_code: null == kaijyo_code
          ? _value.kaijyo_code
          : kaijyo_code // ignore: cast_nullable_to_non_nullable
              as String,
      kaijyo_name: null == kaijyo_name
          ? _value.kaijyo_name
          : kaijyo_name // ignore: cast_nullable_to_non_nullable
              as String,
      kaijyo_kana: null == kaijyo_kana
          ? _value.kaijyo_kana
          : kaijyo_kana // ignore: cast_nullable_to_non_nullable
              as String,
      zip_code: null == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String,
      addr1: null == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String,
      addr2: null == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String,
      tel_no: null == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String,
      fax_no: null == fax_no
          ? _value.fax_no
          : fax_no // ignore: cast_nullable_to_non_nullable
              as String,
      memo: null == memo
          ? _value.memo
          : memo // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LocationImpl implements _Location {
  const _$LocationImpl(
      {required this.kaijyo_code,
      required this.kaijyo_name,
      required this.kaijyo_kana,
      required this.zip_code,
      required this.addr1,
      required this.addr2,
      required this.tel_no,
      required this.fax_no,
      required this.memo});

  factory _$LocationImpl.fromJson(Map<String, dynamic> json) =>
      _$$LocationImplFromJson(json);

  @override
  final String kaijyo_code;
  @override
  final String kaijyo_name;
  @override
  final String kaijyo_kana;
  @override
  final String zip_code;
// 郵便番号
  @override
  final String addr1;
// 住所1
  @override
  final String addr2;
// 住所2
  @override
  final String tel_no;
// 電話番号
  @override
  final String fax_no;
// FAX番号
  @override
  final String memo;

  @override
  String toString() {
    return 'Location(kaijyo_code: $kaijyo_code, kaijyo_name: $kaijyo_name, kaijyo_kana: $kaijyo_kana, zip_code: $zip_code, addr1: $addr1, addr2: $addr2, tel_no: $tel_no, fax_no: $fax_no, memo: $memo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LocationImpl &&
            (identical(other.kaijyo_code, kaijyo_code) ||
                other.kaijyo_code == kaijyo_code) &&
            (identical(other.kaijyo_name, kaijyo_name) ||
                other.kaijyo_name == kaijyo_name) &&
            (identical(other.kaijyo_kana, kaijyo_kana) ||
                other.kaijyo_kana == kaijyo_kana) &&
            (identical(other.zip_code, zip_code) ||
                other.zip_code == zip_code) &&
            (identical(other.addr1, addr1) || other.addr1 == addr1) &&
            (identical(other.addr2, addr2) || other.addr2 == addr2) &&
            (identical(other.tel_no, tel_no) || other.tel_no == tel_no) &&
            (identical(other.fax_no, fax_no) || other.fax_no == fax_no) &&
            (identical(other.memo, memo) || other.memo == memo));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, kaijyo_code, kaijyo_name,
      kaijyo_kana, zip_code, addr1, addr2, tel_no, fax_no, memo);

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LocationImplCopyWith<_$LocationImpl> get copyWith =>
      __$$LocationImplCopyWithImpl<_$LocationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LocationImplToJson(
      this,
    );
  }
}

abstract class _Location implements Location {
  const factory _Location(
      {required final String kaijyo_code,
      required final String kaijyo_name,
      required final String kaijyo_kana,
      required final String zip_code,
      required final String addr1,
      required final String addr2,
      required final String tel_no,
      required final String fax_no,
      required final String memo}) = _$LocationImpl;

  factory _Location.fromJson(Map<String, dynamic> json) =
      _$LocationImpl.fromJson;

  @override
  String get kaijyo_code;
  @override
  String get kaijyo_name;
  @override
  String get kaijyo_kana;
  @override
  String get zip_code; // 郵便番号
  @override
  String get addr1; // 住所1
  @override
  String get addr2; // 住所2
  @override
  String get tel_no; // 電話番号
  @override
  String get fax_no; // FAX番号
  @override
  String get memo;

  /// Create a copy of Location
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LocationImplCopyWith<_$LocationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
