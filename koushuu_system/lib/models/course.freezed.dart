// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Course _$CourseFromJson(Map<String, dynamic> json) {
  return _Course.fromJson(json);
}

/// @nodoc
mixin _$Course {
  String get kosyu_number => throw _privateConstructorUsedError; // 講習コード
  String get kosyu_code => throw _privateConstructorUsedError; // 講習コード
  String get kosyu_name => throw _privateConstructorUsedError; // 講習名
  String get kosyu_omit => throw _privateConstructorUsedError; // 講習名略
  String get jimusyo_code => throw _privateConstructorUsedError; // 事務所コード
  String get jimusyo_name => throw _privateConstructorUsedError; // 事務所名
  String get kaijyo_code => throw _privateConstructorUsedError; // 会場コード
  String get kaijyo_name => throw _privateConstructorUsedError; // 会場名
  String get subtitle => throw _privateConstructorUsedError; // サブタイトル
// required String? course_code, // コースコード
// required String course_name, // コース名
// required int course_days, // コース日数
// required int price, // 口数金額
  int get attend_days_count => throw _privateConstructorUsedError; // 受講日件数
  List<AttendDay> get attend_days_list => throw _privateConstructorUsedError;

  /// Serializes this Course to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Course
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CourseCopyWith<Course> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseCopyWith<$Res> {
  factory $CourseCopyWith(Course value, $Res Function(Course) then) =
      _$CourseCopyWithImpl<$Res, Course>;
  @useResult
  $Res call(
      {String kosyu_number,
      String kosyu_code,
      String kosyu_name,
      String kosyu_omit,
      String jimusyo_code,
      String jimusyo_name,
      String kaijyo_code,
      String kaijyo_name,
      String subtitle,
      int attend_days_count,
      List<AttendDay> attend_days_list});
}

/// @nodoc
class _$CourseCopyWithImpl<$Res, $Val extends Course>
    implements $CourseCopyWith<$Res> {
  _$CourseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Course
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? kosyu_number = null,
    Object? kosyu_code = null,
    Object? kosyu_name = null,
    Object? kosyu_omit = null,
    Object? jimusyo_code = null,
    Object? jimusyo_name = null,
    Object? kaijyo_code = null,
    Object? kaijyo_name = null,
    Object? subtitle = null,
    Object? attend_days_count = null,
    Object? attend_days_list = null,
  }) {
    return _then(_value.copyWith(
      kosyu_number: null == kosyu_number
          ? _value.kosyu_number
          : kosyu_number // ignore: cast_nullable_to_non_nullable
              as String,
      kosyu_code: null == kosyu_code
          ? _value.kosyu_code
          : kosyu_code // ignore: cast_nullable_to_non_nullable
              as String,
      kosyu_name: null == kosyu_name
          ? _value.kosyu_name
          : kosyu_name // ignore: cast_nullable_to_non_nullable
              as String,
      kosyu_omit: null == kosyu_omit
          ? _value.kosyu_omit
          : kosyu_omit // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_code: null == jimusyo_code
          ? _value.jimusyo_code
          : jimusyo_code // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_name: null == jimusyo_name
          ? _value.jimusyo_name
          : jimusyo_name // ignore: cast_nullable_to_non_nullable
              as String,
      kaijyo_code: null == kaijyo_code
          ? _value.kaijyo_code
          : kaijyo_code // ignore: cast_nullable_to_non_nullable
              as String,
      kaijyo_name: null == kaijyo_name
          ? _value.kaijyo_name
          : kaijyo_name // ignore: cast_nullable_to_non_nullable
              as String,
      subtitle: null == subtitle
          ? _value.subtitle
          : subtitle // ignore: cast_nullable_to_non_nullable
              as String,
      attend_days_count: null == attend_days_count
          ? _value.attend_days_count
          : attend_days_count // ignore: cast_nullable_to_non_nullable
              as int,
      attend_days_list: null == attend_days_list
          ? _value.attend_days_list
          : attend_days_list // ignore: cast_nullable_to_non_nullable
              as List<AttendDay>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CourseImplCopyWith<$Res> implements $CourseCopyWith<$Res> {
  factory _$$CourseImplCopyWith(
          _$CourseImpl value, $Res Function(_$CourseImpl) then) =
      __$$CourseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String kosyu_number,
      String kosyu_code,
      String kosyu_name,
      String kosyu_omit,
      String jimusyo_code,
      String jimusyo_name,
      String kaijyo_code,
      String kaijyo_name,
      String subtitle,
      int attend_days_count,
      List<AttendDay> attend_days_list});
}

/// @nodoc
class __$$CourseImplCopyWithImpl<$Res>
    extends _$CourseCopyWithImpl<$Res, _$CourseImpl>
    implements _$$CourseImplCopyWith<$Res> {
  __$$CourseImplCopyWithImpl(
      _$CourseImpl _value, $Res Function(_$CourseImpl) _then)
      : super(_value, _then);

  /// Create a copy of Course
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? kosyu_number = null,
    Object? kosyu_code = null,
    Object? kosyu_name = null,
    Object? kosyu_omit = null,
    Object? jimusyo_code = null,
    Object? jimusyo_name = null,
    Object? kaijyo_code = null,
    Object? kaijyo_name = null,
    Object? subtitle = null,
    Object? attend_days_count = null,
    Object? attend_days_list = null,
  }) {
    return _then(_$CourseImpl(
      kosyu_number: null == kosyu_number
          ? _value.kosyu_number
          : kosyu_number // ignore: cast_nullable_to_non_nullable
              as String,
      kosyu_code: null == kosyu_code
          ? _value.kosyu_code
          : kosyu_code // ignore: cast_nullable_to_non_nullable
              as String,
      kosyu_name: null == kosyu_name
          ? _value.kosyu_name
          : kosyu_name // ignore: cast_nullable_to_non_nullable
              as String,
      kosyu_omit: null == kosyu_omit
          ? _value.kosyu_omit
          : kosyu_omit // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_code: null == jimusyo_code
          ? _value.jimusyo_code
          : jimusyo_code // ignore: cast_nullable_to_non_nullable
              as String,
      jimusyo_name: null == jimusyo_name
          ? _value.jimusyo_name
          : jimusyo_name // ignore: cast_nullable_to_non_nullable
              as String,
      kaijyo_code: null == kaijyo_code
          ? _value.kaijyo_code
          : kaijyo_code // ignore: cast_nullable_to_non_nullable
              as String,
      kaijyo_name: null == kaijyo_name
          ? _value.kaijyo_name
          : kaijyo_name // ignore: cast_nullable_to_non_nullable
              as String,
      subtitle: null == subtitle
          ? _value.subtitle
          : subtitle // ignore: cast_nullable_to_non_nullable
              as String,
      attend_days_count: null == attend_days_count
          ? _value.attend_days_count
          : attend_days_count // ignore: cast_nullable_to_non_nullable
              as int,
      attend_days_list: null == attend_days_list
          ? _value._attend_days_list
          : attend_days_list // ignore: cast_nullable_to_non_nullable
              as List<AttendDay>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CourseImpl implements _Course {
  const _$CourseImpl(
      {required this.kosyu_number,
      required this.kosyu_code,
      required this.kosyu_name,
      required this.kosyu_omit,
      required this.jimusyo_code,
      required this.jimusyo_name,
      required this.kaijyo_code,
      required this.kaijyo_name,
      required this.subtitle,
      required this.attend_days_count,
      required final List<AttendDay> attend_days_list})
      : _attend_days_list = attend_days_list;

  factory _$CourseImpl.fromJson(Map<String, dynamic> json) =>
      _$$CourseImplFromJson(json);

  @override
  final String kosyu_number;
// 講習コード
  @override
  final String kosyu_code;
// 講習コード
  @override
  final String kosyu_name;
// 講習名
  @override
  final String kosyu_omit;
// 講習名略
  @override
  final String jimusyo_code;
// 事務所コード
  @override
  final String jimusyo_name;
// 事務所名
  @override
  final String kaijyo_code;
// 会場コード
  @override
  final String kaijyo_name;
// 会場名
  @override
  final String subtitle;
// サブタイトル
// required String? course_code, // コースコード
// required String course_name, // コース名
// required int course_days, // コース日数
// required int price, // 口数金額
  @override
  final int attend_days_count;
// 受講日件数
  final List<AttendDay> _attend_days_list;
// 受講日件数
  @override
  List<AttendDay> get attend_days_list {
    if (_attend_days_list is EqualUnmodifiableListView)
      return _attend_days_list;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_attend_days_list);
  }

  @override
  String toString() {
    return 'Course(kosyu_number: $kosyu_number, kosyu_code: $kosyu_code, kosyu_name: $kosyu_name, kosyu_omit: $kosyu_omit, jimusyo_code: $jimusyo_code, jimusyo_name: $jimusyo_name, kaijyo_code: $kaijyo_code, kaijyo_name: $kaijyo_name, subtitle: $subtitle, attend_days_count: $attend_days_count, attend_days_list: $attend_days_list)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CourseImpl &&
            (identical(other.kosyu_number, kosyu_number) ||
                other.kosyu_number == kosyu_number) &&
            (identical(other.kosyu_code, kosyu_code) ||
                other.kosyu_code == kosyu_code) &&
            (identical(other.kosyu_name, kosyu_name) ||
                other.kosyu_name == kosyu_name) &&
            (identical(other.kosyu_omit, kosyu_omit) ||
                other.kosyu_omit == kosyu_omit) &&
            (identical(other.jimusyo_code, jimusyo_code) ||
                other.jimusyo_code == jimusyo_code) &&
            (identical(other.jimusyo_name, jimusyo_name) ||
                other.jimusyo_name == jimusyo_name) &&
            (identical(other.kaijyo_code, kaijyo_code) ||
                other.kaijyo_code == kaijyo_code) &&
            (identical(other.kaijyo_name, kaijyo_name) ||
                other.kaijyo_name == kaijyo_name) &&
            (identical(other.subtitle, subtitle) ||
                other.subtitle == subtitle) &&
            (identical(other.attend_days_count, attend_days_count) ||
                other.attend_days_count == attend_days_count) &&
            const DeepCollectionEquality()
                .equals(other._attend_days_list, _attend_days_list));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      kosyu_number,
      kosyu_code,
      kosyu_name,
      kosyu_omit,
      jimusyo_code,
      jimusyo_name,
      kaijyo_code,
      kaijyo_name,
      subtitle,
      attend_days_count,
      const DeepCollectionEquality().hash(_attend_days_list));

  /// Create a copy of Course
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CourseImplCopyWith<_$CourseImpl> get copyWith =>
      __$$CourseImplCopyWithImpl<_$CourseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CourseImplToJson(
      this,
    );
  }
}

abstract class _Course implements Course {
  const factory _Course(
      {required final String kosyu_number,
      required final String kosyu_code,
      required final String kosyu_name,
      required final String kosyu_omit,
      required final String jimusyo_code,
      required final String jimusyo_name,
      required final String kaijyo_code,
      required final String kaijyo_name,
      required final String subtitle,
      required final int attend_days_count,
      required final List<AttendDay> attend_days_list}) = _$CourseImpl;

  factory _Course.fromJson(Map<String, dynamic> json) = _$CourseImpl.fromJson;

  @override
  String get kosyu_number; // 講習コード
  @override
  String get kosyu_code; // 講習コード
  @override
  String get kosyu_name; // 講習名
  @override
  String get kosyu_omit; // 講習名略
  @override
  String get jimusyo_code; // 事務所コード
  @override
  String get jimusyo_name; // 事務所名
  @override
  String get kaijyo_code; // 会場コード
  @override
  String get kaijyo_name; // 会場名
  @override
  String get subtitle; // サブタイトル
// required String? course_code, // コースコード
// required String course_name, // コース名
// required int course_days, // コース日数
// required int price, // 口数金額
  @override
  int get attend_days_count; // 受講日件数
  @override
  List<AttendDay> get attend_days_list;

  /// Create a copy of Course
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CourseImplCopyWith<_$CourseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AttendDay _$AttendDayFromJson(Map<String, dynamic> json) {
  return _AttendDay.fromJson(json);
}

/// @nodoc
mixin _$AttendDay {
  String get start_date => throw _privateConstructorUsedError; // 開始日
  String get end_date => throw _privateConstructorUsedError; // 終了日
  int get request_count => throw _privateConstructorUsedError; // 申込数
  int get capacity_count => throw _privateConstructorUsedError; // 定員数
  int get suspended_flag => throw _privateConstructorUsedError; // 中止フラグ
  String get course_code => throw _privateConstructorUsedError; // コースコード
  String get course_name => throw _privateConstructorUsedError; // コース名
  int get course_days => throw _privateConstructorUsedError; // コース日数
  int get price => throw _privateConstructorUsedError;

  /// Serializes this AttendDay to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AttendDay
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AttendDayCopyWith<AttendDay> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AttendDayCopyWith<$Res> {
  factory $AttendDayCopyWith(AttendDay value, $Res Function(AttendDay) then) =
      _$AttendDayCopyWithImpl<$Res, AttendDay>;
  @useResult
  $Res call(
      {String start_date,
      String end_date,
      int request_count,
      int capacity_count,
      int suspended_flag,
      String course_code,
      String course_name,
      int course_days,
      int price});
}

/// @nodoc
class _$AttendDayCopyWithImpl<$Res, $Val extends AttendDay>
    implements $AttendDayCopyWith<$Res> {
  _$AttendDayCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AttendDay
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? start_date = null,
    Object? end_date = null,
    Object? request_count = null,
    Object? capacity_count = null,
    Object? suspended_flag = null,
    Object? course_code = null,
    Object? course_name = null,
    Object? course_days = null,
    Object? price = null,
  }) {
    return _then(_value.copyWith(
      start_date: null == start_date
          ? _value.start_date
          : start_date // ignore: cast_nullable_to_non_nullable
              as String,
      end_date: null == end_date
          ? _value.end_date
          : end_date // ignore: cast_nullable_to_non_nullable
              as String,
      request_count: null == request_count
          ? _value.request_count
          : request_count // ignore: cast_nullable_to_non_nullable
              as int,
      capacity_count: null == capacity_count
          ? _value.capacity_count
          : capacity_count // ignore: cast_nullable_to_non_nullable
              as int,
      suspended_flag: null == suspended_flag
          ? _value.suspended_flag
          : suspended_flag // ignore: cast_nullable_to_non_nullable
              as int,
      course_code: null == course_code
          ? _value.course_code
          : course_code // ignore: cast_nullable_to_non_nullable
              as String,
      course_name: null == course_name
          ? _value.course_name
          : course_name // ignore: cast_nullable_to_non_nullable
              as String,
      course_days: null == course_days
          ? _value.course_days
          : course_days // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AttendDayImplCopyWith<$Res>
    implements $AttendDayCopyWith<$Res> {
  factory _$$AttendDayImplCopyWith(
          _$AttendDayImpl value, $Res Function(_$AttendDayImpl) then) =
      __$$AttendDayImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String start_date,
      String end_date,
      int request_count,
      int capacity_count,
      int suspended_flag,
      String course_code,
      String course_name,
      int course_days,
      int price});
}

/// @nodoc
class __$$AttendDayImplCopyWithImpl<$Res>
    extends _$AttendDayCopyWithImpl<$Res, _$AttendDayImpl>
    implements _$$AttendDayImplCopyWith<$Res> {
  __$$AttendDayImplCopyWithImpl(
      _$AttendDayImpl _value, $Res Function(_$AttendDayImpl) _then)
      : super(_value, _then);

  /// Create a copy of AttendDay
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? start_date = null,
    Object? end_date = null,
    Object? request_count = null,
    Object? capacity_count = null,
    Object? suspended_flag = null,
    Object? course_code = null,
    Object? course_name = null,
    Object? course_days = null,
    Object? price = null,
  }) {
    return _then(_$AttendDayImpl(
      start_date: null == start_date
          ? _value.start_date
          : start_date // ignore: cast_nullable_to_non_nullable
              as String,
      end_date: null == end_date
          ? _value.end_date
          : end_date // ignore: cast_nullable_to_non_nullable
              as String,
      request_count: null == request_count
          ? _value.request_count
          : request_count // ignore: cast_nullable_to_non_nullable
              as int,
      capacity_count: null == capacity_count
          ? _value.capacity_count
          : capacity_count // ignore: cast_nullable_to_non_nullable
              as int,
      suspended_flag: null == suspended_flag
          ? _value.suspended_flag
          : suspended_flag // ignore: cast_nullable_to_non_nullable
              as int,
      course_code: null == course_code
          ? _value.course_code
          : course_code // ignore: cast_nullable_to_non_nullable
              as String,
      course_name: null == course_name
          ? _value.course_name
          : course_name // ignore: cast_nullable_to_non_nullable
              as String,
      course_days: null == course_days
          ? _value.course_days
          : course_days // ignore: cast_nullable_to_non_nullable
              as int,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AttendDayImpl implements _AttendDay {
  const _$AttendDayImpl(
      {required this.start_date,
      required this.end_date,
      required this.request_count,
      required this.capacity_count,
      required this.suspended_flag,
      required this.course_code,
      required this.course_name,
      required this.course_days,
      required this.price});

  factory _$AttendDayImpl.fromJson(Map<String, dynamic> json) =>
      _$$AttendDayImplFromJson(json);

  @override
  final String start_date;
// 開始日
  @override
  final String end_date;
// 終了日
  @override
  final int request_count;
// 申込数
  @override
  final int capacity_count;
// 定員数
  @override
  final int suspended_flag;
// 中止フラグ
  @override
  final String course_code;
// コースコード
  @override
  final String course_name;
// コース名
  @override
  final int course_days;
// コース日数
  @override
  final int price;

  @override
  String toString() {
    return 'AttendDay(start_date: $start_date, end_date: $end_date, request_count: $request_count, capacity_count: $capacity_count, suspended_flag: $suspended_flag, course_code: $course_code, course_name: $course_name, course_days: $course_days, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AttendDayImpl &&
            (identical(other.start_date, start_date) ||
                other.start_date == start_date) &&
            (identical(other.end_date, end_date) ||
                other.end_date == end_date) &&
            (identical(other.request_count, request_count) ||
                other.request_count == request_count) &&
            (identical(other.capacity_count, capacity_count) ||
                other.capacity_count == capacity_count) &&
            (identical(other.suspended_flag, suspended_flag) ||
                other.suspended_flag == suspended_flag) &&
            (identical(other.course_code, course_code) ||
                other.course_code == course_code) &&
            (identical(other.course_name, course_name) ||
                other.course_name == course_name) &&
            (identical(other.course_days, course_days) ||
                other.course_days == course_days) &&
            (identical(other.price, price) || other.price == price));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      start_date,
      end_date,
      request_count,
      capacity_count,
      suspended_flag,
      course_code,
      course_name,
      course_days,
      price);

  /// Create a copy of AttendDay
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AttendDayImplCopyWith<_$AttendDayImpl> get copyWith =>
      __$$AttendDayImplCopyWithImpl<_$AttendDayImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AttendDayImplToJson(
      this,
    );
  }
}

abstract class _AttendDay implements AttendDay {
  const factory _AttendDay(
      {required final String start_date,
      required final String end_date,
      required final int request_count,
      required final int capacity_count,
      required final int suspended_flag,
      required final String course_code,
      required final String course_name,
      required final int course_days,
      required final int price}) = _$AttendDayImpl;

  factory _AttendDay.fromJson(Map<String, dynamic> json) =
      _$AttendDayImpl.fromJson;

  @override
  String get start_date; // 開始日
  @override
  String get end_date; // 終了日
  @override
  int get request_count; // 申込数
  @override
  int get capacity_count; // 定員数
  @override
  int get suspended_flag; // 中止フラグ
  @override
  String get course_code; // コースコード
  @override
  String get course_name; // コース名
  @override
  int get course_days; // コース日数
  @override
  int get price;

  /// Create a copy of AttendDay
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AttendDayImplCopyWith<_$AttendDayImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
