// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pref.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$PrefImpl _$$PrefImplFromJson(Map<String, dynamic> json) => _$PrefImpl(
      jimusyo_code: json['jimusyo_code'] as String,
      pref_code: (json['pref_code'] as num).toInt(),
      pref_name: json['pref_name'] as String,
      jimusyo_name: json['jimusyo_name'] as String,
      jimusyo_kana: json['jimusyo_kana'] as String,
      zip_code: json['zip_code'] as String,
      addr1: json['addr1'] as String,
      addr2: json['addr2'] as String,
      tel_no: json['tel_no'] as String,
      fax_no: json['fax_no'] as String,
      top_id: json['top_id'] as String,
      jimusyo_code_formal: json['jimusyo_code_formal'] as String,
      jimusyo_code_parent: json['jimusyo_code_parent'] as String,
      kosyu_jimusyo_flag: (json['kosyu_jimusyo_flag'] as num).toInt(),
      text_sales_flag: (json['text_sales_flag'] as num).toInt(),
      memo: json['memo'] as String,
    );

Map<String, dynamic> _$$PrefImplToJson(_$PrefImpl instance) =>
    <String, dynamic>{
      'jimusyo_code': instance.jimusyo_code,
      'pref_code': instance.pref_code,
      'pref_name': instance.pref_name,
      'jimusyo_name': instance.jimusyo_name,
      'jimusyo_kana': instance.jimusyo_kana,
      'zip_code': instance.zip_code,
      'addr1': instance.addr1,
      'addr2': instance.addr2,
      'tel_no': instance.tel_no,
      'fax_no': instance.fax_no,
      'top_id': instance.top_id,
      'jimusyo_code_formal': instance.jimusyo_code_formal,
      'jimusyo_code_parent': instance.jimusyo_code_parent,
      'kosyu_jimusyo_flag': instance.kosyu_jimusyo_flag,
      'text_sales_flag': instance.text_sales_flag,
      'memo': instance.memo,
    };
