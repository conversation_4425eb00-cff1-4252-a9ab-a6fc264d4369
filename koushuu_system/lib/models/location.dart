// This file is "main.dart"
import 'package:freezed_annotation/freezed_annotation.dart';

part 'location.freezed.dart';
part 'location.g.dart';

@freezed
class Location with _$Location {
  const factory Location({
    required String kaijyo_code,
    required String kaijyo_name,
    required String kaijyo_kana,
    required String zip_code, // 郵便番号
    required String addr1, // 住所1
    required String addr2, // 住所2
    required String tel_no, // 電話番号
    required String fax_no, // FAX番号
    required String memo, // メモ
  }) = _Location;

  factory Location.fromJson(Map<String, Object?> json) =>
      _$LocationFromJson(json);
}
