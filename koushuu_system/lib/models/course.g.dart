// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'course.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$CourseImpl _$$CourseImplFromJson(Map<String, dynamic> json) => _$CourseImpl(
      kosyu_number: json['kosyu_number'] as String,
      kosyu_code: json['kosyu_code'] as String,
      kosyu_name: json['kosyu_name'] as String,
      kosyu_omit: json['kosyu_omit'] as String,
      jimusyo_code: json['jimusyo_code'] as String,
      jimusyo_name: json['jimusyo_name'] as String,
      kaijyo_code: json['kaijyo_code'] as String,
      kaijyo_name: json['kaijyo_name'] as String,
      subtitle: json['subtitle'] as String,
      attend_days_count: (json['attend_days_count'] as num).toInt(),
      attend_days_list: (json['attend_days_list'] as List<dynamic>)
          .map((e) => AttendDay.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$CourseImplToJson(_$CourseImpl instance) =>
    <String, dynamic>{
      'kosyu_number': instance.kosyu_number,
      'kosyu_code': instance.kosyu_code,
      'kosyu_name': instance.kosyu_name,
      'kosyu_omit': instance.kosyu_omit,
      'jimusyo_code': instance.jimusyo_code,
      'jimusyo_name': instance.jimusyo_name,
      'kaijyo_code': instance.kaijyo_code,
      'kaijyo_name': instance.kaijyo_name,
      'subtitle': instance.subtitle,
      'attend_days_count': instance.attend_days_count,
      'attend_days_list': instance.attend_days_list,
    };

_$AttendDayImpl _$$AttendDayImplFromJson(Map<String, dynamic> json) =>
    _$AttendDayImpl(
      start_date: json['start_date'] as String,
      end_date: json['end_date'] as String,
      request_count: (json['request_count'] as num).toInt(),
      capacity_count: (json['capacity_count'] as num).toInt(),
      suspended_flag: (json['suspended_flag'] as num).toInt(),
      course_code: json['course_code'] as String,
      course_name: json['course_name'] as String,
      course_days: (json['course_days'] as num).toInt(),
      price: (json['price'] as num).toInt(),
    );

Map<String, dynamic> _$$AttendDayImplToJson(_$AttendDayImpl instance) =>
    <String, dynamic>{
      'start_date': instance.start_date,
      'end_date': instance.end_date,
      'request_count': instance.request_count,
      'capacity_count': instance.capacity_count,
      'suspended_flag': instance.suspended_flag,
      'course_code': instance.course_code,
      'course_name': instance.course_name,
      'course_days': instance.course_days,
      'price': instance.price,
    };
