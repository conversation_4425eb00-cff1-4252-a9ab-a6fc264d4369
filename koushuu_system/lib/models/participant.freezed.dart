// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'participant.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Participant _$ParticipantFromJson(Map<String, dynamic> json) {
  return _Participant.fromJson(json);
}

/// @nodoc
mixin _$Participant {
  int? get id => throw _privateConstructorUsedError;
  int? get application_id => throw _privateConstructorUsedError; // 申込ID
  String? get uketsuke_id => throw _privateConstructorUsedError; // 受付ID
  String? get kosyu_number => throw _privateConstructorUsedError; // 講習番号
  int? get kosyu_code => throw _privateConstructorUsedError; // 講習コード
  String? get kosyu_date => throw _privateConstructorUsedError; // 講習日付
  int? get course_code => throw _privateConstructorUsedError; // コースコード
  int? get jimusyo_code => throw _privateConstructorUsedError; // 事務所コード
  int? get kaijyo_code => throw _privateConstructorUsedError; // 会場コード
  String? get applicant_number => throw _privateConstructorUsedError; // 申込番号
  String? get uketsuke_date => throw _privateConstructorUsedError; // 申込番号
  int? get applicant_code => throw _privateConstructorUsedError; // 申込番号
  String? get name1 => throw _privateConstructorUsedError; // 氏名1
  String? get name2 => throw _privateConstructorUsedError; // 氏名2
  String? get name_kana => throw _privateConstructorUsedError; // 氏名カナ
  int? get old_or_common_name_type =>
      throw _privateConstructorUsedError; // 旧姓通称区分
  String? get reason => throw _privateConstructorUsedError; // 旧姓通称区分
  String? get name3 => throw _privateConstructorUsedError; // 氏名3
  String? get birth_day => throw _privateConstructorUsedError; // 生年月日
  String? get zip_code => throw _privateConstructorUsedError; // 郵便番号
  String? get addr1 => throw _privateConstructorUsedError; // 現住所1
  String? get addr2 => throw _privateConstructorUsedError; // 現住所2
  String? get tel_no => throw _privateConstructorUsedError; // 電話番号
  String? get mail_addr => throw _privateConstructorUsedError; // メールアドレス
  String? get opt_company => throw _privateConstructorUsedError;
  int? get unacquired_kosyu_code =>
      throw _privateConstructorUsedError; // 未取得講習コード
  String? get declaration_party_name =>
      throw _privateConstructorUsedError; // 申告団体名
  String? get declaration_date => throw _privateConstructorUsedError; // 申告交付日付
  int? get soufu_kubun => throw _privateConstructorUsedError; // 送付先区分
  String? get url => throw _privateConstructorUsedError; // 入力フォームURL
  String? get image_photo =>
      throw _privateConstructorUsedError; // 本人写真画像データ(Base64)
  String? get filename_photo =>
      throw _privateConstructorUsedError; // 本人写真画像データ(Base64)
  String? get filetype_photo =>
      throw _privateConstructorUsedError; // 本人写真画像データ(Base64)
  String? get image_kakunin_omote =>
      throw _privateConstructorUsedError; // 本人確認(表)画像データ(Base64)
  String? get filename_kakunin_omote =>
      throw _privateConstructorUsedError; // 本人確認(表)画像データ(Base64)
  String? get filetype_kakunin_omote =>
      throw _privateConstructorUsedError; // 本人確認(表)画像データ(Base64)
  String? get image_kakunin_ura =>
      throw _privateConstructorUsedError; // 本人確認(裏)画像データ(Base64)
  String? get filename_kakunin_ura =>
      throw _privateConstructorUsedError; // 本人確認(裏)画像データ(Base64)
  String? get filetype_kakunin_ura =>
      throw _privateConstructorUsedError; // 本人確認(裏)画像データ(Base64)
  String? get image_kakunin_atsumi =>
      throw _privateConstructorUsedError; // 本人確認(厚み)画像データ(Base64)
  String? get filename_kakunin_atsumi =>
      throw _privateConstructorUsedError; // 本人確認(厚み)画像データ(Base64)
  String? get filetype_kakunin_atsumi =>
      throw _privateConstructorUsedError; // 本人確認(厚み)画像データ(Base64)
  String? get image_license1 =>
      throw _privateConstructorUsedError; // 受講資格1画像データ(Base64)
  String? get filename_license1 =>
      throw _privateConstructorUsedError; // 受講資格1画像データ(Base64)
  String? get filetype_license1 =>
      throw _privateConstructorUsedError; // 受講資格1画像データ(Base64)
  String? get image_license2 =>
      throw _privateConstructorUsedError; // 受講資格2画像データ(Base64)
  String? get filename_license2 =>
      throw _privateConstructorUsedError; // 受講資格2画像データ(Base64)
  String? get filetype_license2 =>
      throw _privateConstructorUsedError; // 受講資格2画像データ(Base64)
  String? get image_license3 =>
      throw _privateConstructorUsedError; // 受講資格3画像データ(Base64)
  String? get filename_license3 =>
      throw _privateConstructorUsedError; // 受講資格3画像データ(Base64)
  String? get filetype_license3 =>
      throw _privateConstructorUsedError; // 受講資格3画像データ(Base64)
  int? get sinkoku_jimusyo_code => throw _privateConstructorUsedError;
  int? get sinkoku_kosyu_kubun => throw _privateConstructorUsedError;
  String? get sinkoku_kosyu_date => throw _privateConstructorUsedError;
  int? get sinkoku_kuwari_kubun => throw _privateConstructorUsedError;
  String? get unacquired_kosyu_code_title => throw _privateConstructorUsedError;
  String? get sinkoku_jimusyo_code_title => throw _privateConstructorUsedError;
  String? get sinkoku_kosyu_kubun_title => throw _privateConstructorUsedError;
  String? get sinkoku_kuwari_kubun_title => throw _privateConstructorUsedError;

  /// Serializes this Participant to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Participant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ParticipantCopyWith<Participant> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ParticipantCopyWith<$Res> {
  factory $ParticipantCopyWith(
          Participant value, $Res Function(Participant) then) =
      _$ParticipantCopyWithImpl<$Res, Participant>;
  @useResult
  $Res call(
      {int? id,
      int? application_id,
      String? uketsuke_id,
      String? kosyu_number,
      int? kosyu_code,
      String? kosyu_date,
      int? course_code,
      int? jimusyo_code,
      int? kaijyo_code,
      String? applicant_number,
      String? uketsuke_date,
      int? applicant_code,
      String? name1,
      String? name2,
      String? name_kana,
      int? old_or_common_name_type,
      String? reason,
      String? name3,
      String? birth_day,
      String? zip_code,
      String? addr1,
      String? addr2,
      String? tel_no,
      String? mail_addr,
      String? opt_company,
      int? unacquired_kosyu_code,
      String? declaration_party_name,
      String? declaration_date,
      int? soufu_kubun,
      String? url,
      String? image_photo,
      String? filename_photo,
      String? filetype_photo,
      String? image_kakunin_omote,
      String? filename_kakunin_omote,
      String? filetype_kakunin_omote,
      String? image_kakunin_ura,
      String? filename_kakunin_ura,
      String? filetype_kakunin_ura,
      String? image_kakunin_atsumi,
      String? filename_kakunin_atsumi,
      String? filetype_kakunin_atsumi,
      String? image_license1,
      String? filename_license1,
      String? filetype_license1,
      String? image_license2,
      String? filename_license2,
      String? filetype_license2,
      String? image_license3,
      String? filename_license3,
      String? filetype_license3,
      int? sinkoku_jimusyo_code,
      int? sinkoku_kosyu_kubun,
      String? sinkoku_kosyu_date,
      int? sinkoku_kuwari_kubun,
      String? unacquired_kosyu_code_title,
      String? sinkoku_jimusyo_code_title,
      String? sinkoku_kosyu_kubun_title,
      String? sinkoku_kuwari_kubun_title});
}

/// @nodoc
class _$ParticipantCopyWithImpl<$Res, $Val extends Participant>
    implements $ParticipantCopyWith<$Res> {
  _$ParticipantCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Participant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? application_id = freezed,
    Object? uketsuke_id = freezed,
    Object? kosyu_number = freezed,
    Object? kosyu_code = freezed,
    Object? kosyu_date = freezed,
    Object? course_code = freezed,
    Object? jimusyo_code = freezed,
    Object? kaijyo_code = freezed,
    Object? applicant_number = freezed,
    Object? uketsuke_date = freezed,
    Object? applicant_code = freezed,
    Object? name1 = freezed,
    Object? name2 = freezed,
    Object? name_kana = freezed,
    Object? old_or_common_name_type = freezed,
    Object? reason = freezed,
    Object? name3 = freezed,
    Object? birth_day = freezed,
    Object? zip_code = freezed,
    Object? addr1 = freezed,
    Object? addr2 = freezed,
    Object? tel_no = freezed,
    Object? mail_addr = freezed,
    Object? opt_company = freezed,
    Object? unacquired_kosyu_code = freezed,
    Object? declaration_party_name = freezed,
    Object? declaration_date = freezed,
    Object? soufu_kubun = freezed,
    Object? url = freezed,
    Object? image_photo = freezed,
    Object? filename_photo = freezed,
    Object? filetype_photo = freezed,
    Object? image_kakunin_omote = freezed,
    Object? filename_kakunin_omote = freezed,
    Object? filetype_kakunin_omote = freezed,
    Object? image_kakunin_ura = freezed,
    Object? filename_kakunin_ura = freezed,
    Object? filetype_kakunin_ura = freezed,
    Object? image_kakunin_atsumi = freezed,
    Object? filename_kakunin_atsumi = freezed,
    Object? filetype_kakunin_atsumi = freezed,
    Object? image_license1 = freezed,
    Object? filename_license1 = freezed,
    Object? filetype_license1 = freezed,
    Object? image_license2 = freezed,
    Object? filename_license2 = freezed,
    Object? filetype_license2 = freezed,
    Object? image_license3 = freezed,
    Object? filename_license3 = freezed,
    Object? filetype_license3 = freezed,
    Object? sinkoku_jimusyo_code = freezed,
    Object? sinkoku_kosyu_kubun = freezed,
    Object? sinkoku_kosyu_date = freezed,
    Object? sinkoku_kuwari_kubun = freezed,
    Object? unacquired_kosyu_code_title = freezed,
    Object? sinkoku_jimusyo_code_title = freezed,
    Object? sinkoku_kosyu_kubun_title = freezed,
    Object? sinkoku_kuwari_kubun_title = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      application_id: freezed == application_id
          ? _value.application_id
          : application_id // ignore: cast_nullable_to_non_nullable
              as int?,
      uketsuke_id: freezed == uketsuke_id
          ? _value.uketsuke_id
          : uketsuke_id // ignore: cast_nullable_to_non_nullable
              as String?,
      kosyu_number: freezed == kosyu_number
          ? _value.kosyu_number
          : kosyu_number // ignore: cast_nullable_to_non_nullable
              as String?,
      kosyu_code: freezed == kosyu_code
          ? _value.kosyu_code
          : kosyu_code // ignore: cast_nullable_to_non_nullable
              as int?,
      kosyu_date: freezed == kosyu_date
          ? _value.kosyu_date
          : kosyu_date // ignore: cast_nullable_to_non_nullable
              as String?,
      course_code: freezed == course_code
          ? _value.course_code
          : course_code // ignore: cast_nullable_to_non_nullable
              as int?,
      jimusyo_code: freezed == jimusyo_code
          ? _value.jimusyo_code
          : jimusyo_code // ignore: cast_nullable_to_non_nullable
              as int?,
      kaijyo_code: freezed == kaijyo_code
          ? _value.kaijyo_code
          : kaijyo_code // ignore: cast_nullable_to_non_nullable
              as int?,
      applicant_number: freezed == applicant_number
          ? _value.applicant_number
          : applicant_number // ignore: cast_nullable_to_non_nullable
              as String?,
      uketsuke_date: freezed == uketsuke_date
          ? _value.uketsuke_date
          : uketsuke_date // ignore: cast_nullable_to_non_nullable
              as String?,
      applicant_code: freezed == applicant_code
          ? _value.applicant_code
          : applicant_code // ignore: cast_nullable_to_non_nullable
              as int?,
      name1: freezed == name1
          ? _value.name1
          : name1 // ignore: cast_nullable_to_non_nullable
              as String?,
      name2: freezed == name2
          ? _value.name2
          : name2 // ignore: cast_nullable_to_non_nullable
              as String?,
      name_kana: freezed == name_kana
          ? _value.name_kana
          : name_kana // ignore: cast_nullable_to_non_nullable
              as String?,
      old_or_common_name_type: freezed == old_or_common_name_type
          ? _value.old_or_common_name_type
          : old_or_common_name_type // ignore: cast_nullable_to_non_nullable
              as int?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      name3: freezed == name3
          ? _value.name3
          : name3 // ignore: cast_nullable_to_non_nullable
              as String?,
      birth_day: freezed == birth_day
          ? _value.birth_day
          : birth_day // ignore: cast_nullable_to_non_nullable
              as String?,
      zip_code: freezed == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String?,
      addr1: freezed == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String?,
      addr2: freezed == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String?,
      tel_no: freezed == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String?,
      mail_addr: freezed == mail_addr
          ? _value.mail_addr
          : mail_addr // ignore: cast_nullable_to_non_nullable
              as String?,
      opt_company: freezed == opt_company
          ? _value.opt_company
          : opt_company // ignore: cast_nullable_to_non_nullable
              as String?,
      unacquired_kosyu_code: freezed == unacquired_kosyu_code
          ? _value.unacquired_kosyu_code
          : unacquired_kosyu_code // ignore: cast_nullable_to_non_nullable
              as int?,
      declaration_party_name: freezed == declaration_party_name
          ? _value.declaration_party_name
          : declaration_party_name // ignore: cast_nullable_to_non_nullable
              as String?,
      declaration_date: freezed == declaration_date
          ? _value.declaration_date
          : declaration_date // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_kubun: freezed == soufu_kubun
          ? _value.soufu_kubun
          : soufu_kubun // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      image_photo: freezed == image_photo
          ? _value.image_photo
          : image_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_photo: freezed == filename_photo
          ? _value.filename_photo
          : filename_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_photo: freezed == filetype_photo
          ? _value.filetype_photo
          : filetype_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_omote: freezed == image_kakunin_omote
          ? _value.image_kakunin_omote
          : image_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_omote: freezed == filename_kakunin_omote
          ? _value.filename_kakunin_omote
          : filename_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_omote: freezed == filetype_kakunin_omote
          ? _value.filetype_kakunin_omote
          : filetype_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_ura: freezed == image_kakunin_ura
          ? _value.image_kakunin_ura
          : image_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_ura: freezed == filename_kakunin_ura
          ? _value.filename_kakunin_ura
          : filename_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_ura: freezed == filetype_kakunin_ura
          ? _value.filetype_kakunin_ura
          : filetype_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_atsumi: freezed == image_kakunin_atsumi
          ? _value.image_kakunin_atsumi
          : image_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_atsumi: freezed == filename_kakunin_atsumi
          ? _value.filename_kakunin_atsumi
          : filename_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_atsumi: freezed == filetype_kakunin_atsumi
          ? _value.filetype_kakunin_atsumi
          : filetype_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      image_license1: freezed == image_license1
          ? _value.image_license1
          : image_license1 // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_license1: freezed == filename_license1
          ? _value.filename_license1
          : filename_license1 // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_license1: freezed == filetype_license1
          ? _value.filetype_license1
          : filetype_license1 // ignore: cast_nullable_to_non_nullable
              as String?,
      image_license2: freezed == image_license2
          ? _value.image_license2
          : image_license2 // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_license2: freezed == filename_license2
          ? _value.filename_license2
          : filename_license2 // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_license2: freezed == filetype_license2
          ? _value.filetype_license2
          : filetype_license2 // ignore: cast_nullable_to_non_nullable
              as String?,
      image_license3: freezed == image_license3
          ? _value.image_license3
          : image_license3 // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_license3: freezed == filename_license3
          ? _value.filename_license3
          : filename_license3 // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_license3: freezed == filetype_license3
          ? _value.filetype_license3
          : filetype_license3 // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_jimusyo_code: freezed == sinkoku_jimusyo_code
          ? _value.sinkoku_jimusyo_code
          : sinkoku_jimusyo_code // ignore: cast_nullable_to_non_nullable
              as int?,
      sinkoku_kosyu_kubun: freezed == sinkoku_kosyu_kubun
          ? _value.sinkoku_kosyu_kubun
          : sinkoku_kosyu_kubun // ignore: cast_nullable_to_non_nullable
              as int?,
      sinkoku_kosyu_date: freezed == sinkoku_kosyu_date
          ? _value.sinkoku_kosyu_date
          : sinkoku_kosyu_date // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_kuwari_kubun: freezed == sinkoku_kuwari_kubun
          ? _value.sinkoku_kuwari_kubun
          : sinkoku_kuwari_kubun // ignore: cast_nullable_to_non_nullable
              as int?,
      unacquired_kosyu_code_title: freezed == unacquired_kosyu_code_title
          ? _value.unacquired_kosyu_code_title
          : unacquired_kosyu_code_title // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_jimusyo_code_title: freezed == sinkoku_jimusyo_code_title
          ? _value.sinkoku_jimusyo_code_title
          : sinkoku_jimusyo_code_title // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_kosyu_kubun_title: freezed == sinkoku_kosyu_kubun_title
          ? _value.sinkoku_kosyu_kubun_title
          : sinkoku_kosyu_kubun_title // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_kuwari_kubun_title: freezed == sinkoku_kuwari_kubun_title
          ? _value.sinkoku_kuwari_kubun_title
          : sinkoku_kuwari_kubun_title // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ParticipantImplCopyWith<$Res>
    implements $ParticipantCopyWith<$Res> {
  factory _$$ParticipantImplCopyWith(
          _$ParticipantImpl value, $Res Function(_$ParticipantImpl) then) =
      __$$ParticipantImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      int? application_id,
      String? uketsuke_id,
      String? kosyu_number,
      int? kosyu_code,
      String? kosyu_date,
      int? course_code,
      int? jimusyo_code,
      int? kaijyo_code,
      String? applicant_number,
      String? uketsuke_date,
      int? applicant_code,
      String? name1,
      String? name2,
      String? name_kana,
      int? old_or_common_name_type,
      String? reason,
      String? name3,
      String? birth_day,
      String? zip_code,
      String? addr1,
      String? addr2,
      String? tel_no,
      String? mail_addr,
      String? opt_company,
      int? unacquired_kosyu_code,
      String? declaration_party_name,
      String? declaration_date,
      int? soufu_kubun,
      String? url,
      String? image_photo,
      String? filename_photo,
      String? filetype_photo,
      String? image_kakunin_omote,
      String? filename_kakunin_omote,
      String? filetype_kakunin_omote,
      String? image_kakunin_ura,
      String? filename_kakunin_ura,
      String? filetype_kakunin_ura,
      String? image_kakunin_atsumi,
      String? filename_kakunin_atsumi,
      String? filetype_kakunin_atsumi,
      String? image_license1,
      String? filename_license1,
      String? filetype_license1,
      String? image_license2,
      String? filename_license2,
      String? filetype_license2,
      String? image_license3,
      String? filename_license3,
      String? filetype_license3,
      int? sinkoku_jimusyo_code,
      int? sinkoku_kosyu_kubun,
      String? sinkoku_kosyu_date,
      int? sinkoku_kuwari_kubun,
      String? unacquired_kosyu_code_title,
      String? sinkoku_jimusyo_code_title,
      String? sinkoku_kosyu_kubun_title,
      String? sinkoku_kuwari_kubun_title});
}

/// @nodoc
class __$$ParticipantImplCopyWithImpl<$Res>
    extends _$ParticipantCopyWithImpl<$Res, _$ParticipantImpl>
    implements _$$ParticipantImplCopyWith<$Res> {
  __$$ParticipantImplCopyWithImpl(
      _$ParticipantImpl _value, $Res Function(_$ParticipantImpl) _then)
      : super(_value, _then);

  /// Create a copy of Participant
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? application_id = freezed,
    Object? uketsuke_id = freezed,
    Object? kosyu_number = freezed,
    Object? kosyu_code = freezed,
    Object? kosyu_date = freezed,
    Object? course_code = freezed,
    Object? jimusyo_code = freezed,
    Object? kaijyo_code = freezed,
    Object? applicant_number = freezed,
    Object? uketsuke_date = freezed,
    Object? applicant_code = freezed,
    Object? name1 = freezed,
    Object? name2 = freezed,
    Object? name_kana = freezed,
    Object? old_or_common_name_type = freezed,
    Object? reason = freezed,
    Object? name3 = freezed,
    Object? birth_day = freezed,
    Object? zip_code = freezed,
    Object? addr1 = freezed,
    Object? addr2 = freezed,
    Object? tel_no = freezed,
    Object? mail_addr = freezed,
    Object? opt_company = freezed,
    Object? unacquired_kosyu_code = freezed,
    Object? declaration_party_name = freezed,
    Object? declaration_date = freezed,
    Object? soufu_kubun = freezed,
    Object? url = freezed,
    Object? image_photo = freezed,
    Object? filename_photo = freezed,
    Object? filetype_photo = freezed,
    Object? image_kakunin_omote = freezed,
    Object? filename_kakunin_omote = freezed,
    Object? filetype_kakunin_omote = freezed,
    Object? image_kakunin_ura = freezed,
    Object? filename_kakunin_ura = freezed,
    Object? filetype_kakunin_ura = freezed,
    Object? image_kakunin_atsumi = freezed,
    Object? filename_kakunin_atsumi = freezed,
    Object? filetype_kakunin_atsumi = freezed,
    Object? image_license1 = freezed,
    Object? filename_license1 = freezed,
    Object? filetype_license1 = freezed,
    Object? image_license2 = freezed,
    Object? filename_license2 = freezed,
    Object? filetype_license2 = freezed,
    Object? image_license3 = freezed,
    Object? filename_license3 = freezed,
    Object? filetype_license3 = freezed,
    Object? sinkoku_jimusyo_code = freezed,
    Object? sinkoku_kosyu_kubun = freezed,
    Object? sinkoku_kosyu_date = freezed,
    Object? sinkoku_kuwari_kubun = freezed,
    Object? unacquired_kosyu_code_title = freezed,
    Object? sinkoku_jimusyo_code_title = freezed,
    Object? sinkoku_kosyu_kubun_title = freezed,
    Object? sinkoku_kuwari_kubun_title = freezed,
  }) {
    return _then(_$ParticipantImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      application_id: freezed == application_id
          ? _value.application_id
          : application_id // ignore: cast_nullable_to_non_nullable
              as int?,
      uketsuke_id: freezed == uketsuke_id
          ? _value.uketsuke_id
          : uketsuke_id // ignore: cast_nullable_to_non_nullable
              as String?,
      kosyu_number: freezed == kosyu_number
          ? _value.kosyu_number
          : kosyu_number // ignore: cast_nullable_to_non_nullable
              as String?,
      kosyu_code: freezed == kosyu_code
          ? _value.kosyu_code
          : kosyu_code // ignore: cast_nullable_to_non_nullable
              as int?,
      kosyu_date: freezed == kosyu_date
          ? _value.kosyu_date
          : kosyu_date // ignore: cast_nullable_to_non_nullable
              as String?,
      course_code: freezed == course_code
          ? _value.course_code
          : course_code // ignore: cast_nullable_to_non_nullable
              as int?,
      jimusyo_code: freezed == jimusyo_code
          ? _value.jimusyo_code
          : jimusyo_code // ignore: cast_nullable_to_non_nullable
              as int?,
      kaijyo_code: freezed == kaijyo_code
          ? _value.kaijyo_code
          : kaijyo_code // ignore: cast_nullable_to_non_nullable
              as int?,
      applicant_number: freezed == applicant_number
          ? _value.applicant_number
          : applicant_number // ignore: cast_nullable_to_non_nullable
              as String?,
      uketsuke_date: freezed == uketsuke_date
          ? _value.uketsuke_date
          : uketsuke_date // ignore: cast_nullable_to_non_nullable
              as String?,
      applicant_code: freezed == applicant_code
          ? _value.applicant_code
          : applicant_code // ignore: cast_nullable_to_non_nullable
              as int?,
      name1: freezed == name1
          ? _value.name1
          : name1 // ignore: cast_nullable_to_non_nullable
              as String?,
      name2: freezed == name2
          ? _value.name2
          : name2 // ignore: cast_nullable_to_non_nullable
              as String?,
      name_kana: freezed == name_kana
          ? _value.name_kana
          : name_kana // ignore: cast_nullable_to_non_nullable
              as String?,
      old_or_common_name_type: freezed == old_or_common_name_type
          ? _value.old_or_common_name_type
          : old_or_common_name_type // ignore: cast_nullable_to_non_nullable
              as int?,
      reason: freezed == reason
          ? _value.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      name3: freezed == name3
          ? _value.name3
          : name3 // ignore: cast_nullable_to_non_nullable
              as String?,
      birth_day: freezed == birth_day
          ? _value.birth_day
          : birth_day // ignore: cast_nullable_to_non_nullable
              as String?,
      zip_code: freezed == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String?,
      addr1: freezed == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String?,
      addr2: freezed == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String?,
      tel_no: freezed == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String?,
      mail_addr: freezed == mail_addr
          ? _value.mail_addr
          : mail_addr // ignore: cast_nullable_to_non_nullable
              as String?,
      opt_company: freezed == opt_company
          ? _value.opt_company
          : opt_company // ignore: cast_nullable_to_non_nullable
              as String?,
      unacquired_kosyu_code: freezed == unacquired_kosyu_code
          ? _value.unacquired_kosyu_code
          : unacquired_kosyu_code // ignore: cast_nullable_to_non_nullable
              as int?,
      declaration_party_name: freezed == declaration_party_name
          ? _value.declaration_party_name
          : declaration_party_name // ignore: cast_nullable_to_non_nullable
              as String?,
      declaration_date: freezed == declaration_date
          ? _value.declaration_date
          : declaration_date // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_kubun: freezed == soufu_kubun
          ? _value.soufu_kubun
          : soufu_kubun // ignore: cast_nullable_to_non_nullable
              as int?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      image_photo: freezed == image_photo
          ? _value.image_photo
          : image_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_photo: freezed == filename_photo
          ? _value.filename_photo
          : filename_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_photo: freezed == filetype_photo
          ? _value.filetype_photo
          : filetype_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_omote: freezed == image_kakunin_omote
          ? _value.image_kakunin_omote
          : image_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_omote: freezed == filename_kakunin_omote
          ? _value.filename_kakunin_omote
          : filename_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_omote: freezed == filetype_kakunin_omote
          ? _value.filetype_kakunin_omote
          : filetype_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_ura: freezed == image_kakunin_ura
          ? _value.image_kakunin_ura
          : image_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_ura: freezed == filename_kakunin_ura
          ? _value.filename_kakunin_ura
          : filename_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_ura: freezed == filetype_kakunin_ura
          ? _value.filetype_kakunin_ura
          : filetype_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_atsumi: freezed == image_kakunin_atsumi
          ? _value.image_kakunin_atsumi
          : image_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_atsumi: freezed == filename_kakunin_atsumi
          ? _value.filename_kakunin_atsumi
          : filename_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_atsumi: freezed == filetype_kakunin_atsumi
          ? _value.filetype_kakunin_atsumi
          : filetype_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      image_license1: freezed == image_license1
          ? _value.image_license1
          : image_license1 // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_license1: freezed == filename_license1
          ? _value.filename_license1
          : filename_license1 // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_license1: freezed == filetype_license1
          ? _value.filetype_license1
          : filetype_license1 // ignore: cast_nullable_to_non_nullable
              as String?,
      image_license2: freezed == image_license2
          ? _value.image_license2
          : image_license2 // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_license2: freezed == filename_license2
          ? _value.filename_license2
          : filename_license2 // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_license2: freezed == filetype_license2
          ? _value.filetype_license2
          : filetype_license2 // ignore: cast_nullable_to_non_nullable
              as String?,
      image_license3: freezed == image_license3
          ? _value.image_license3
          : image_license3 // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_license3: freezed == filename_license3
          ? _value.filename_license3
          : filename_license3 // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_license3: freezed == filetype_license3
          ? _value.filetype_license3
          : filetype_license3 // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_jimusyo_code: freezed == sinkoku_jimusyo_code
          ? _value.sinkoku_jimusyo_code
          : sinkoku_jimusyo_code // ignore: cast_nullable_to_non_nullable
              as int?,
      sinkoku_kosyu_kubun: freezed == sinkoku_kosyu_kubun
          ? _value.sinkoku_kosyu_kubun
          : sinkoku_kosyu_kubun // ignore: cast_nullable_to_non_nullable
              as int?,
      sinkoku_kosyu_date: freezed == sinkoku_kosyu_date
          ? _value.sinkoku_kosyu_date
          : sinkoku_kosyu_date // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_kuwari_kubun: freezed == sinkoku_kuwari_kubun
          ? _value.sinkoku_kuwari_kubun
          : sinkoku_kuwari_kubun // ignore: cast_nullable_to_non_nullable
              as int?,
      unacquired_kosyu_code_title: freezed == unacquired_kosyu_code_title
          ? _value.unacquired_kosyu_code_title
          : unacquired_kosyu_code_title // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_jimusyo_code_title: freezed == sinkoku_jimusyo_code_title
          ? _value.sinkoku_jimusyo_code_title
          : sinkoku_jimusyo_code_title // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_kosyu_kubun_title: freezed == sinkoku_kosyu_kubun_title
          ? _value.sinkoku_kosyu_kubun_title
          : sinkoku_kosyu_kubun_title // ignore: cast_nullable_to_non_nullable
              as String?,
      sinkoku_kuwari_kubun_title: freezed == sinkoku_kuwari_kubun_title
          ? _value.sinkoku_kuwari_kubun_title
          : sinkoku_kuwari_kubun_title // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ParticipantImpl implements _Participant {
  const _$ParticipantImpl(
      {this.id,
      this.application_id,
      this.uketsuke_id,
      this.kosyu_number,
      this.kosyu_code,
      this.kosyu_date,
      this.course_code,
      this.jimusyo_code,
      this.kaijyo_code,
      this.applicant_number,
      this.uketsuke_date,
      this.applicant_code,
      this.name1,
      this.name2,
      this.name_kana,
      this.old_or_common_name_type,
      this.reason,
      this.name3,
      this.birth_day,
      this.zip_code,
      this.addr1,
      this.addr2,
      this.tel_no,
      this.mail_addr,
      this.opt_company,
      this.unacquired_kosyu_code,
      this.declaration_party_name,
      this.declaration_date,
      this.soufu_kubun,
      this.url,
      this.image_photo,
      this.filename_photo,
      this.filetype_photo,
      this.image_kakunin_omote,
      this.filename_kakunin_omote,
      this.filetype_kakunin_omote,
      this.image_kakunin_ura,
      this.filename_kakunin_ura,
      this.filetype_kakunin_ura,
      this.image_kakunin_atsumi,
      this.filename_kakunin_atsumi,
      this.filetype_kakunin_atsumi,
      this.image_license1,
      this.filename_license1,
      this.filetype_license1,
      this.image_license2,
      this.filename_license2,
      this.filetype_license2,
      this.image_license3,
      this.filename_license3,
      this.filetype_license3,
      this.sinkoku_jimusyo_code,
      this.sinkoku_kosyu_kubun,
      this.sinkoku_kosyu_date,
      this.sinkoku_kuwari_kubun,
      this.unacquired_kosyu_code_title,
      this.sinkoku_jimusyo_code_title,
      this.sinkoku_kosyu_kubun_title,
      this.sinkoku_kuwari_kubun_title});

  factory _$ParticipantImpl.fromJson(Map<String, dynamic> json) =>
      _$$ParticipantImplFromJson(json);

  @override
  final int? id;
  @override
  final int? application_id;
// 申込ID
  @override
  final String? uketsuke_id;
// 受付ID
  @override
  final String? kosyu_number;
// 講習番号
  @override
  final int? kosyu_code;
// 講習コード
  @override
  final String? kosyu_date;
// 講習日付
  @override
  final int? course_code;
// コースコード
  @override
  final int? jimusyo_code;
// 事務所コード
  @override
  final int? kaijyo_code;
// 会場コード
  @override
  final String? applicant_number;
// 申込番号
  @override
  final String? uketsuke_date;
// 申込番号
  @override
  final int? applicant_code;
// 申込番号
  @override
  final String? name1;
// 氏名1
  @override
  final String? name2;
// 氏名2
  @override
  final String? name_kana;
// 氏名カナ
  @override
  final int? old_or_common_name_type;
// 旧姓通称区分
  @override
  final String? reason;
// 旧姓通称区分
  @override
  final String? name3;
// 氏名3
  @override
  final String? birth_day;
// 生年月日
  @override
  final String? zip_code;
// 郵便番号
  @override
  final String? addr1;
// 現住所1
  @override
  final String? addr2;
// 現住所2
  @override
  final String? tel_no;
// 電話番号
  @override
  final String? mail_addr;
// メールアドレス
  @override
  final String? opt_company;
  @override
  final int? unacquired_kosyu_code;
// 未取得講習コード
  @override
  final String? declaration_party_name;
// 申告団体名
  @override
  final String? declaration_date;
// 申告交付日付
  @override
  final int? soufu_kubun;
// 送付先区分
  @override
  final String? url;
// 入力フォームURL
  @override
  final String? image_photo;
// 本人写真画像データ(Base64)
  @override
  final String? filename_photo;
// 本人写真画像データ(Base64)
  @override
  final String? filetype_photo;
// 本人写真画像データ(Base64)
  @override
  final String? image_kakunin_omote;
// 本人確認(表)画像データ(Base64)
  @override
  final String? filename_kakunin_omote;
// 本人確認(表)画像データ(Base64)
  @override
  final String? filetype_kakunin_omote;
// 本人確認(表)画像データ(Base64)
  @override
  final String? image_kakunin_ura;
// 本人確認(裏)画像データ(Base64)
  @override
  final String? filename_kakunin_ura;
// 本人確認(裏)画像データ(Base64)
  @override
  final String? filetype_kakunin_ura;
// 本人確認(裏)画像データ(Base64)
  @override
  final String? image_kakunin_atsumi;
// 本人確認(厚み)画像データ(Base64)
  @override
  final String? filename_kakunin_atsumi;
// 本人確認(厚み)画像データ(Base64)
  @override
  final String? filetype_kakunin_atsumi;
// 本人確認(厚み)画像データ(Base64)
  @override
  final String? image_license1;
// 受講資格1画像データ(Base64)
  @override
  final String? filename_license1;
// 受講資格1画像データ(Base64)
  @override
  final String? filetype_license1;
// 受講資格1画像データ(Base64)
  @override
  final String? image_license2;
// 受講資格2画像データ(Base64)
  @override
  final String? filename_license2;
// 受講資格2画像データ(Base64)
  @override
  final String? filetype_license2;
// 受講資格2画像データ(Base64)
  @override
  final String? image_license3;
// 受講資格3画像データ(Base64)
  @override
  final String? filename_license3;
// 受講資格3画像データ(Base64)
  @override
  final String? filetype_license3;
// 受講資格3画像データ(Base64)
  @override
  final int? sinkoku_jimusyo_code;
  @override
  final int? sinkoku_kosyu_kubun;
  @override
  final String? sinkoku_kosyu_date;
  @override
  final int? sinkoku_kuwari_kubun;
  @override
  final String? unacquired_kosyu_code_title;
  @override
  final String? sinkoku_jimusyo_code_title;
  @override
  final String? sinkoku_kosyu_kubun_title;
  @override
  final String? sinkoku_kuwari_kubun_title;

  @override
  String toString() {
    return 'Participant(id: $id, application_id: $application_id, uketsuke_id: $uketsuke_id, kosyu_number: $kosyu_number, kosyu_code: $kosyu_code, kosyu_date: $kosyu_date, course_code: $course_code, jimusyo_code: $jimusyo_code, kaijyo_code: $kaijyo_code, applicant_number: $applicant_number, uketsuke_date: $uketsuke_date, applicant_code: $applicant_code, name1: $name1, name2: $name2, name_kana: $name_kana, old_or_common_name_type: $old_or_common_name_type, reason: $reason, name3: $name3, birth_day: $birth_day, zip_code: $zip_code, addr1: $addr1, addr2: $addr2, tel_no: $tel_no, mail_addr: $mail_addr, opt_company: $opt_company, unacquired_kosyu_code: $unacquired_kosyu_code, declaration_party_name: $declaration_party_name, declaration_date: $declaration_date, soufu_kubun: $soufu_kubun, url: $url, image_photo: $image_photo, filename_photo: $filename_photo, filetype_photo: $filetype_photo, image_kakunin_omote: $image_kakunin_omote, filename_kakunin_omote: $filename_kakunin_omote, filetype_kakunin_omote: $filetype_kakunin_omote, image_kakunin_ura: $image_kakunin_ura, filename_kakunin_ura: $filename_kakunin_ura, filetype_kakunin_ura: $filetype_kakunin_ura, image_kakunin_atsumi: $image_kakunin_atsumi, filename_kakunin_atsumi: $filename_kakunin_atsumi, filetype_kakunin_atsumi: $filetype_kakunin_atsumi, image_license1: $image_license1, filename_license1: $filename_license1, filetype_license1: $filetype_license1, image_license2: $image_license2, filename_license2: $filename_license2, filetype_license2: $filetype_license2, image_license3: $image_license3, filename_license3: $filename_license3, filetype_license3: $filetype_license3, sinkoku_jimusyo_code: $sinkoku_jimusyo_code, sinkoku_kosyu_kubun: $sinkoku_kosyu_kubun, sinkoku_kosyu_date: $sinkoku_kosyu_date, sinkoku_kuwari_kubun: $sinkoku_kuwari_kubun, unacquired_kosyu_code_title: $unacquired_kosyu_code_title, sinkoku_jimusyo_code_title: $sinkoku_jimusyo_code_title, sinkoku_kosyu_kubun_title: $sinkoku_kosyu_kubun_title, sinkoku_kuwari_kubun_title: $sinkoku_kuwari_kubun_title)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ParticipantImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.application_id, application_id) ||
                other.application_id == application_id) &&
            (identical(other.uketsuke_id, uketsuke_id) ||
                other.uketsuke_id == uketsuke_id) &&
            (identical(other.kosyu_number, kosyu_number) ||
                other.kosyu_number == kosyu_number) &&
            (identical(other.kosyu_code, kosyu_code) ||
                other.kosyu_code == kosyu_code) &&
            (identical(other.kosyu_date, kosyu_date) ||
                other.kosyu_date == kosyu_date) &&
            (identical(other.course_code, course_code) ||
                other.course_code == course_code) &&
            (identical(other.jimusyo_code, jimusyo_code) ||
                other.jimusyo_code == jimusyo_code) &&
            (identical(other.kaijyo_code, kaijyo_code) ||
                other.kaijyo_code == kaijyo_code) &&
            (identical(other.applicant_number, applicant_number) ||
                other.applicant_number == applicant_number) &&
            (identical(other.uketsuke_date, uketsuke_date) ||
                other.uketsuke_date == uketsuke_date) &&
            (identical(other.applicant_code, applicant_code) ||
                other.applicant_code == applicant_code) &&
            (identical(other.name1, name1) || other.name1 == name1) &&
            (identical(other.name2, name2) || other.name2 == name2) &&
            (identical(other.name_kana, name_kana) ||
                other.name_kana == name_kana) &&
            (identical(other.old_or_common_name_type, old_or_common_name_type) ||
                other.old_or_common_name_type == old_or_common_name_type) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.name3, name3) || other.name3 == name3) &&
            (identical(other.birth_day, birth_day) ||
                other.birth_day == birth_day) &&
            (identical(other.zip_code, zip_code) ||
                other.zip_code == zip_code) &&
            (identical(other.addr1, addr1) || other.addr1 == addr1) &&
            (identical(other.addr2, addr2) || other.addr2 == addr2) &&
            (identical(other.tel_no, tel_no) || other.tel_no == tel_no) &&
            (identical(other.mail_addr, mail_addr) ||
                other.mail_addr == mail_addr) &&
            (identical(other.opt_company, opt_company) ||
                other.opt_company == opt_company) &&
            (identical(other.unacquired_kosyu_code, unacquired_kosyu_code) ||
                other.unacquired_kosyu_code == unacquired_kosyu_code) &&
            (identical(other.declaration_party_name, declaration_party_name) ||
                other.declaration_party_name == declaration_party_name) &&
            (identical(other.declaration_date, declaration_date) ||
                other.declaration_date == declaration_date) &&
            (identical(other.soufu_kubun, soufu_kubun) ||
                other.soufu_kubun == soufu_kubun) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.image_photo, image_photo) ||
                other.image_photo == image_photo) &&
            (identical(other.filename_photo, filename_photo) ||
                other.filename_photo == filename_photo) &&
            (identical(other.filetype_photo, filetype_photo) ||
                other.filetype_photo == filetype_photo) &&
            (identical(other.image_kakunin_omote, image_kakunin_omote) ||
                other.image_kakunin_omote == image_kakunin_omote) &&
            (identical(other.filename_kakunin_omote, filename_kakunin_omote) ||
                other.filename_kakunin_omote == filename_kakunin_omote) &&
            (identical(other.filetype_kakunin_omote, filetype_kakunin_omote) ||
                other.filetype_kakunin_omote == filetype_kakunin_omote) &&
            (identical(other.image_kakunin_ura, image_kakunin_ura) ||
                other.image_kakunin_ura == image_kakunin_ura) &&
            (identical(other.filename_kakunin_ura, filename_kakunin_ura) ||
                other.filename_kakunin_ura == filename_kakunin_ura) &&
            (identical(other.filetype_kakunin_ura, filetype_kakunin_ura) ||
                other.filetype_kakunin_ura == filetype_kakunin_ura) &&
            (identical(other.image_kakunin_atsumi, image_kakunin_atsumi) ||
                other.image_kakunin_atsumi == image_kakunin_atsumi) &&
            (identical(other.filename_kakunin_atsumi, filename_kakunin_atsumi) ||
                other.filename_kakunin_atsumi == filename_kakunin_atsumi) &&
            (identical(other.filetype_kakunin_atsumi, filetype_kakunin_atsumi) ||
                other.filetype_kakunin_atsumi == filetype_kakunin_atsumi) &&
            (identical(other.image_license1, image_license1) ||
                other.image_license1 == image_license1) &&
            (identical(other.filename_license1, filename_license1) ||
                other.filename_license1 == filename_license1) &&
            (identical(other.filetype_license1, filetype_license1) ||
                other.filetype_license1 == filetype_license1) &&
            (identical(other.image_license2, image_license2) || other.image_license2 == image_license2) &&
            (identical(other.filename_license2, filename_license2) || other.filename_license2 == filename_license2) &&
            (identical(other.filetype_license2, filetype_license2) || other.filetype_license2 == filetype_license2) &&
            (identical(other.image_license3, image_license3) || other.image_license3 == image_license3) &&
            (identical(other.filename_license3, filename_license3) || other.filename_license3 == filename_license3) &&
            (identical(other.filetype_license3, filetype_license3) || other.filetype_license3 == filetype_license3) &&
            (identical(other.sinkoku_jimusyo_code, sinkoku_jimusyo_code) || other.sinkoku_jimusyo_code == sinkoku_jimusyo_code) &&
            (identical(other.sinkoku_kosyu_kubun, sinkoku_kosyu_kubun) || other.sinkoku_kosyu_kubun == sinkoku_kosyu_kubun) &&
            (identical(other.sinkoku_kosyu_date, sinkoku_kosyu_date) || other.sinkoku_kosyu_date == sinkoku_kosyu_date) &&
            (identical(other.sinkoku_kuwari_kubun, sinkoku_kuwari_kubun) || other.sinkoku_kuwari_kubun == sinkoku_kuwari_kubun) &&
            (identical(other.unacquired_kosyu_code_title, unacquired_kosyu_code_title) || other.unacquired_kosyu_code_title == unacquired_kosyu_code_title) &&
            (identical(other.sinkoku_jimusyo_code_title, sinkoku_jimusyo_code_title) || other.sinkoku_jimusyo_code_title == sinkoku_jimusyo_code_title) &&
            (identical(other.sinkoku_kosyu_kubun_title, sinkoku_kosyu_kubun_title) || other.sinkoku_kosyu_kubun_title == sinkoku_kosyu_kubun_title) &&
            (identical(other.sinkoku_kuwari_kubun_title, sinkoku_kuwari_kubun_title) || other.sinkoku_kuwari_kubun_title == sinkoku_kuwari_kubun_title));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        application_id,
        uketsuke_id,
        kosyu_number,
        kosyu_code,
        kosyu_date,
        course_code,
        jimusyo_code,
        kaijyo_code,
        applicant_number,
        uketsuke_date,
        applicant_code,
        name1,
        name2,
        name_kana,
        old_or_common_name_type,
        reason,
        name3,
        birth_day,
        zip_code,
        addr1,
        addr2,
        tel_no,
        mail_addr,
        opt_company,
        unacquired_kosyu_code,
        declaration_party_name,
        declaration_date,
        soufu_kubun,
        url,
        image_photo,
        filename_photo,
        filetype_photo,
        image_kakunin_omote,
        filename_kakunin_omote,
        filetype_kakunin_omote,
        image_kakunin_ura,
        filename_kakunin_ura,
        filetype_kakunin_ura,
        image_kakunin_atsumi,
        filename_kakunin_atsumi,
        filetype_kakunin_atsumi,
        image_license1,
        filename_license1,
        filetype_license1,
        image_license2,
        filename_license2,
        filetype_license2,
        image_license3,
        filename_license3,
        filetype_license3,
        sinkoku_jimusyo_code,
        sinkoku_kosyu_kubun,
        sinkoku_kosyu_date,
        sinkoku_kuwari_kubun,
        unacquired_kosyu_code_title,
        sinkoku_jimusyo_code_title,
        sinkoku_kosyu_kubun_title,
        sinkoku_kuwari_kubun_title
      ]);

  /// Create a copy of Participant
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ParticipantImplCopyWith<_$ParticipantImpl> get copyWith =>
      __$$ParticipantImplCopyWithImpl<_$ParticipantImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ParticipantImplToJson(
      this,
    );
  }
}

abstract class _Participant implements Participant {
  const factory _Participant(
      {final int? id,
      final int? application_id,
      final String? uketsuke_id,
      final String? kosyu_number,
      final int? kosyu_code,
      final String? kosyu_date,
      final int? course_code,
      final int? jimusyo_code,
      final int? kaijyo_code,
      final String? applicant_number,
      final String? uketsuke_date,
      final int? applicant_code,
      final String? name1,
      final String? name2,
      final String? name_kana,
      final int? old_or_common_name_type,
      final String? reason,
      final String? name3,
      final String? birth_day,
      final String? zip_code,
      final String? addr1,
      final String? addr2,
      final String? tel_no,
      final String? mail_addr,
      final String? opt_company,
      final int? unacquired_kosyu_code,
      final String? declaration_party_name,
      final String? declaration_date,
      final int? soufu_kubun,
      final String? url,
      final String? image_photo,
      final String? filename_photo,
      final String? filetype_photo,
      final String? image_kakunin_omote,
      final String? filename_kakunin_omote,
      final String? filetype_kakunin_omote,
      final String? image_kakunin_ura,
      final String? filename_kakunin_ura,
      final String? filetype_kakunin_ura,
      final String? image_kakunin_atsumi,
      final String? filename_kakunin_atsumi,
      final String? filetype_kakunin_atsumi,
      final String? image_license1,
      final String? filename_license1,
      final String? filetype_license1,
      final String? image_license2,
      final String? filename_license2,
      final String? filetype_license2,
      final String? image_license3,
      final String? filename_license3,
      final String? filetype_license3,
      final int? sinkoku_jimusyo_code,
      final int? sinkoku_kosyu_kubun,
      final String? sinkoku_kosyu_date,
      final int? sinkoku_kuwari_kubun,
      final String? unacquired_kosyu_code_title,
      final String? sinkoku_jimusyo_code_title,
      final String? sinkoku_kosyu_kubun_title,
      final String? sinkoku_kuwari_kubun_title}) = _$ParticipantImpl;

  factory _Participant.fromJson(Map<String, dynamic> json) =
      _$ParticipantImpl.fromJson;

  @override
  int? get id;
  @override
  int? get application_id; // 申込ID
  @override
  String? get uketsuke_id; // 受付ID
  @override
  String? get kosyu_number; // 講習番号
  @override
  int? get kosyu_code; // 講習コード
  @override
  String? get kosyu_date; // 講習日付
  @override
  int? get course_code; // コースコード
  @override
  int? get jimusyo_code; // 事務所コード
  @override
  int? get kaijyo_code; // 会場コード
  @override
  String? get applicant_number; // 申込番号
  @override
  String? get uketsuke_date; // 申込番号
  @override
  int? get applicant_code; // 申込番号
  @override
  String? get name1; // 氏名1
  @override
  String? get name2; // 氏名2
  @override
  String? get name_kana; // 氏名カナ
  @override
  int? get old_or_common_name_type; // 旧姓通称区分
  @override
  String? get reason; // 旧姓通称区分
  @override
  String? get name3; // 氏名3
  @override
  String? get birth_day; // 生年月日
  @override
  String? get zip_code; // 郵便番号
  @override
  String? get addr1; // 現住所1
  @override
  String? get addr2; // 現住所2
  @override
  String? get tel_no; // 電話番号
  @override
  String? get mail_addr; // メールアドレス
  @override
  String? get opt_company;
  @override
  int? get unacquired_kosyu_code; // 未取得講習コード
  @override
  String? get declaration_party_name; // 申告団体名
  @override
  String? get declaration_date; // 申告交付日付
  @override
  int? get soufu_kubun; // 送付先区分
  @override
  String? get url; // 入力フォームURL
  @override
  String? get image_photo; // 本人写真画像データ(Base64)
  @override
  String? get filename_photo; // 本人写真画像データ(Base64)
  @override
  String? get filetype_photo; // 本人写真画像データ(Base64)
  @override
  String? get image_kakunin_omote; // 本人確認(表)画像データ(Base64)
  @override
  String? get filename_kakunin_omote; // 本人確認(表)画像データ(Base64)
  @override
  String? get filetype_kakunin_omote; // 本人確認(表)画像データ(Base64)
  @override
  String? get image_kakunin_ura; // 本人確認(裏)画像データ(Base64)
  @override
  String? get filename_kakunin_ura; // 本人確認(裏)画像データ(Base64)
  @override
  String? get filetype_kakunin_ura; // 本人確認(裏)画像データ(Base64)
  @override
  String? get image_kakunin_atsumi; // 本人確認(厚み)画像データ(Base64)
  @override
  String? get filename_kakunin_atsumi; // 本人確認(厚み)画像データ(Base64)
  @override
  String? get filetype_kakunin_atsumi; // 本人確認(厚み)画像データ(Base64)
  @override
  String? get image_license1; // 受講資格1画像データ(Base64)
  @override
  String? get filename_license1; // 受講資格1画像データ(Base64)
  @override
  String? get filetype_license1; // 受講資格1画像データ(Base64)
  @override
  String? get image_license2; // 受講資格2画像データ(Base64)
  @override
  String? get filename_license2; // 受講資格2画像データ(Base64)
  @override
  String? get filetype_license2; // 受講資格2画像データ(Base64)
  @override
  String? get image_license3; // 受講資格3画像データ(Base64)
  @override
  String? get filename_license3; // 受講資格3画像データ(Base64)
  @override
  String? get filetype_license3; // 受講資格3画像データ(Base64)
  @override
  int? get sinkoku_jimusyo_code;
  @override
  int? get sinkoku_kosyu_kubun;
  @override
  String? get sinkoku_kosyu_date;
  @override
  int? get sinkoku_kuwari_kubun;
  @override
  String? get unacquired_kosyu_code_title;
  @override
  String? get sinkoku_jimusyo_code_title;
  @override
  String? get sinkoku_kosyu_kubun_title;
  @override
  String? get sinkoku_kuwari_kubun_title;

  /// Create a copy of Participant
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ParticipantImplCopyWith<_$ParticipantImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
