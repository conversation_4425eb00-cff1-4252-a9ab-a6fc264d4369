// main.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'rerequest.freezed.dart';
part 'rerequest.g.dart';

@freezed
class ReRequest with _$ReRequest {
  const factory ReRequest({
    String? uketsuke_id, // 受付ID
    String? name_sei, // 姓
    String? name_mei, // 名
    String? name_name_kana, // 氏名カナ
    String? old_name, // 旧姓
    String? old_name_kana, // 旧姓カナ
    String? birth_day, // 生年月日
    String? zip_code, // 郵便番号
    String? addr1, // 住所1
    String? addr2, // 住所2
    String? tel_no, // 電話番号
    String? mail_addr, // メールアドレス
    String? kosyu_type, // 講習種類
    String? jimusyo_code, // 事務所コード
    String? lost_flag, // 紛失フラグ
    String? change_name_flag, // 氏名変更フラグ
    String? damage_flag, // 破損汚れフラグ
    String? other_flag, // その他フラグ
    String? other_riyu, // その他理由
    String? comment, // 連絡事項
    String? url, // 入力フォームアクセス用URL
    // ---- Tmp use ---------
    String? license,
    String? reasonTitle,
    String? place,
  }) = _ReRequest;

  factory ReRequest.fromJson(Map<String, Object?> json) =>
      _$ReRequestFromJson(json);
}

@freezed
class ReRequestDoc with _$ReRequestDoc {
  const factory ReRequestDoc({
    String? uketsuke_id, // 受付ID
    String? name, // 名
    String? name_kana, // 氏名カナ
    String? birth_day, // 生年月日
    String? zip_code, // 郵便番号
    String? addr1, // 住所1
    String? addr2, // 住所2
    String? tel_no, // 電話番号
    String? mail_addr, // メールアドレス
    String? soufu_name, // 送付先名
    String? soufu_zip_code, // 送付先郵便番号
    String? soufu_addr1, // 送付先住所1
    String? soufu_addr2, // 送付先住所2
    String? image_photo, // 本人写真画像データ(Base64)
    String? filename_photo, // 本人写真ファイル名
    String? filetype_photo, // 本人写真ファイル形式(MIMEType)
    String? image_kakunin_omote, // 本人確認(表)画像データ(Base64)
    String? filename_kakunin_omote, // 本人確認(表)ファイル名
    String? filetype_kakunin_omote, // 本人確認(表)ファイル形式(MIMEType)
    String? image_kakunin_ura, // 本人確認(裏)画像データ(Base64)
    String? filename_kakunin_ura, // 本人確認(裏)ファイル名
    String? filetype_kakunin_ura, // 本人確認(裏)ファイル形式(MIMEType)
    String? image_kakunin_atsumi, // 本人確認(厚み)画像データ(Base64)
    String? filename_kakunin_atsumi, // 本人確認(厚み)ファイル名
    String? filetype_kakunin_atsumi, // 本人確認(厚み)ファイル形式(MIMEType)
    String? image_kakunin_rename, // 旧姓通称確認画像データ(Base64)
    String? filename_kakunin_rename, // 旧姓通称確認ファイル名
    String? filetype_kakunin_rename, // 旧姓通称確認ファイル形式(MIMEType)
    String? url, // 入力フォームアクセス用URL
    ReRequest? irai_data, // 再発依頼データ
  }) = _ReRequestDoc;

  factory ReRequestDoc.fromJson(Map<String, Object?> json) =>
      _$ReRequestDocFromJson(json);
}
