// This file is "main.dart"
import 'package:freezed_annotation/freezed_annotation.dart';

part 'pref.freezed.dart';
part 'pref.g.dart';

@freezed
class Pref with _$Pref {
  const factory Pref({
    required String jimusyo_code, // 事務所コード
    required int pref_code, // 都道府県コード
    required String pref_name, // 都道府県名
    required String jimusyo_name, // 事務所名
    required String jimusyo_kana, // 事務所名カナ
    required String zip_code, // 郵便番号
    required String addr1, // 住所1
    required String addr2, // 住所2
    required String tel_no, // 電話番号
    required String fax_no, // FAX番号
    required String top_id, // 所長ID
    required String jimusyo_code_formal, // 事務所コード正式
    required String jimusyo_code_parent, // 親事務所コード
    required int kosyu_jimusyo_flag, // 講習会実施事務所フラグ
    required int text_sales_flag, // テキスト販売事務所フラグ
    required String memo, // メモ
  }) = _Pref;

  factory Pref.fromJson(Map<String, Object?> json) => _$PrefFromJson(json);
}
