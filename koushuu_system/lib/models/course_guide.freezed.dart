// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'course_guide.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

CourseGuide _$CourseGuideFromJson(Map<String, dynamic> json) {
  return _CourseGuide.fromJson(json);
}

/// @nodoc
mixin _$CourseGuide {
  String get course_name => throw _privateConstructorUsedError; // 講習コード
  String get hold_licence => throw _privateConstructorUsedError;

  /// Serializes this CourseGuide to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CourseGuide
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CourseGuideCopyWith<CourseGuide> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CourseGuideCopyWith<$Res> {
  factory $CourseGuideCopyWith(
          CourseGuide value, $Res Function(CourseGuide) then) =
      _$CourseGuideCopyWithImpl<$Res, CourseGuide>;
  @useResult
  $Res call({String course_name, String hold_licence});
}

/// @nodoc
class _$CourseGuideCopyWithImpl<$Res, $Val extends CourseGuide>
    implements $CourseGuideCopyWith<$Res> {
  _$CourseGuideCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CourseGuide
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? course_name = null,
    Object? hold_licence = null,
  }) {
    return _then(_value.copyWith(
      course_name: null == course_name
          ? _value.course_name
          : course_name // ignore: cast_nullable_to_non_nullable
              as String,
      hold_licence: null == hold_licence
          ? _value.hold_licence
          : hold_licence // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CourseGuideImplCopyWith<$Res>
    implements $CourseGuideCopyWith<$Res> {
  factory _$$CourseGuideImplCopyWith(
          _$CourseGuideImpl value, $Res Function(_$CourseGuideImpl) then) =
      __$$CourseGuideImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String course_name, String hold_licence});
}

/// @nodoc
class __$$CourseGuideImplCopyWithImpl<$Res>
    extends _$CourseGuideCopyWithImpl<$Res, _$CourseGuideImpl>
    implements _$$CourseGuideImplCopyWith<$Res> {
  __$$CourseGuideImplCopyWithImpl(
      _$CourseGuideImpl _value, $Res Function(_$CourseGuideImpl) _then)
      : super(_value, _then);

  /// Create a copy of CourseGuide
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? course_name = null,
    Object? hold_licence = null,
  }) {
    return _then(_$CourseGuideImpl(
      course_name: null == course_name
          ? _value.course_name
          : course_name // ignore: cast_nullable_to_non_nullable
              as String,
      hold_licence: null == hold_licence
          ? _value.hold_licence
          : hold_licence // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CourseGuideImpl implements _CourseGuide {
  const _$CourseGuideImpl(
      {required this.course_name, required this.hold_licence});

  factory _$CourseGuideImpl.fromJson(Map<String, dynamic> json) =>
      _$$CourseGuideImplFromJson(json);

  @override
  final String course_name;
// 講習コード
  @override
  final String hold_licence;

  @override
  String toString() {
    return 'CourseGuide(course_name: $course_name, hold_licence: $hold_licence)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CourseGuideImpl &&
            (identical(other.course_name, course_name) ||
                other.course_name == course_name) &&
            (identical(other.hold_licence, hold_licence) ||
                other.hold_licence == hold_licence));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, course_name, hold_licence);

  /// Create a copy of CourseGuide
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CourseGuideImplCopyWith<_$CourseGuideImpl> get copyWith =>
      __$$CourseGuideImplCopyWithImpl<_$CourseGuideImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CourseGuideImplToJson(
      this,
    );
  }
}

abstract class _CourseGuide implements CourseGuide {
  const factory _CourseGuide(
      {required final String course_name,
      required final String hold_licence}) = _$CourseGuideImpl;

  factory _CourseGuide.fromJson(Map<String, dynamic> json) =
      _$CourseGuideImpl.fromJson;

  @override
  String get course_name; // 講習コード
  @override
  String get hold_licence;

  /// Create a copy of CourseGuide
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CourseGuideImplCopyWith<_$CourseGuideImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
