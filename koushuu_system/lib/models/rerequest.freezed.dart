// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'rerequest.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ReRequest _$ReRequestFromJson(Map<String, dynamic> json) {
  return _ReRequest.fromJson(json);
}

/// @nodoc
mixin _$ReRequest {
  String? get uketsuke_id => throw _privateConstructorUsedError; // 受付ID
  String? get name_sei => throw _privateConstructorUsedError; // 姓
  String? get name_mei => throw _privateConstructorUsedError; // 名
  String? get name_name_kana => throw _privateConstructorUsedError; // 氏名カナ
  String? get old_name => throw _privateConstructorUsedError; // 旧姓
  String? get old_name_kana => throw _privateConstructorUsedError; // 旧姓カナ
  String? get birth_day => throw _privateConstructorUsedError; // 生年月日
  String? get zip_code => throw _privateConstructorUsedError; // 郵便番号
  String? get addr1 => throw _privateConstructorUsedError; // 住所1
  String? get addr2 => throw _privateConstructorUsedError; // 住所2
  String? get tel_no => throw _privateConstructorUsedError; // 電話番号
  String? get mail_addr => throw _privateConstructorUsedError; // メールアドレス
  String? get kosyu_type => throw _privateConstructorUsedError; // 講習種類
  String? get jimusyo_code => throw _privateConstructorUsedError; // 事務所コード
  String? get lost_flag => throw _privateConstructorUsedError; // 紛失フラグ
  String? get change_name_flag => throw _privateConstructorUsedError; // 氏名変更フラグ
  String? get damage_flag => throw _privateConstructorUsedError; // 破損汚れフラグ
  String? get other_flag => throw _privateConstructorUsedError; // その他フラグ
  String? get other_riyu => throw _privateConstructorUsedError; // その他理由
  String? get comment => throw _privateConstructorUsedError; // 連絡事項
  String? get url => throw _privateConstructorUsedError; // 入力フォームアクセス用URL
// ---- Tmp use ---------
  String? get license => throw _privateConstructorUsedError;
  String? get reasonTitle => throw _privateConstructorUsedError;
  String? get place => throw _privateConstructorUsedError;

  /// Serializes this ReRequest to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReRequestCopyWith<ReRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReRequestCopyWith<$Res> {
  factory $ReRequestCopyWith(ReRequest value, $Res Function(ReRequest) then) =
      _$ReRequestCopyWithImpl<$Res, ReRequest>;
  @useResult
  $Res call(
      {String? uketsuke_id,
      String? name_sei,
      String? name_mei,
      String? name_name_kana,
      String? old_name,
      String? old_name_kana,
      String? birth_day,
      String? zip_code,
      String? addr1,
      String? addr2,
      String? tel_no,
      String? mail_addr,
      String? kosyu_type,
      String? jimusyo_code,
      String? lost_flag,
      String? change_name_flag,
      String? damage_flag,
      String? other_flag,
      String? other_riyu,
      String? comment,
      String? url,
      String? license,
      String? reasonTitle,
      String? place});
}

/// @nodoc
class _$ReRequestCopyWithImpl<$Res, $Val extends ReRequest>
    implements $ReRequestCopyWith<$Res> {
  _$ReRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uketsuke_id = freezed,
    Object? name_sei = freezed,
    Object? name_mei = freezed,
    Object? name_name_kana = freezed,
    Object? old_name = freezed,
    Object? old_name_kana = freezed,
    Object? birth_day = freezed,
    Object? zip_code = freezed,
    Object? addr1 = freezed,
    Object? addr2 = freezed,
    Object? tel_no = freezed,
    Object? mail_addr = freezed,
    Object? kosyu_type = freezed,
    Object? jimusyo_code = freezed,
    Object? lost_flag = freezed,
    Object? change_name_flag = freezed,
    Object? damage_flag = freezed,
    Object? other_flag = freezed,
    Object? other_riyu = freezed,
    Object? comment = freezed,
    Object? url = freezed,
    Object? license = freezed,
    Object? reasonTitle = freezed,
    Object? place = freezed,
  }) {
    return _then(_value.copyWith(
      uketsuke_id: freezed == uketsuke_id
          ? _value.uketsuke_id
          : uketsuke_id // ignore: cast_nullable_to_non_nullable
              as String?,
      name_sei: freezed == name_sei
          ? _value.name_sei
          : name_sei // ignore: cast_nullable_to_non_nullable
              as String?,
      name_mei: freezed == name_mei
          ? _value.name_mei
          : name_mei // ignore: cast_nullable_to_non_nullable
              as String?,
      name_name_kana: freezed == name_name_kana
          ? _value.name_name_kana
          : name_name_kana // ignore: cast_nullable_to_non_nullable
              as String?,
      old_name: freezed == old_name
          ? _value.old_name
          : old_name // ignore: cast_nullable_to_non_nullable
              as String?,
      old_name_kana: freezed == old_name_kana
          ? _value.old_name_kana
          : old_name_kana // ignore: cast_nullable_to_non_nullable
              as String?,
      birth_day: freezed == birth_day
          ? _value.birth_day
          : birth_day // ignore: cast_nullable_to_non_nullable
              as String?,
      zip_code: freezed == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String?,
      addr1: freezed == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String?,
      addr2: freezed == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String?,
      tel_no: freezed == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String?,
      mail_addr: freezed == mail_addr
          ? _value.mail_addr
          : mail_addr // ignore: cast_nullable_to_non_nullable
              as String?,
      kosyu_type: freezed == kosyu_type
          ? _value.kosyu_type
          : kosyu_type // ignore: cast_nullable_to_non_nullable
              as String?,
      jimusyo_code: freezed == jimusyo_code
          ? _value.jimusyo_code
          : jimusyo_code // ignore: cast_nullable_to_non_nullable
              as String?,
      lost_flag: freezed == lost_flag
          ? _value.lost_flag
          : lost_flag // ignore: cast_nullable_to_non_nullable
              as String?,
      change_name_flag: freezed == change_name_flag
          ? _value.change_name_flag
          : change_name_flag // ignore: cast_nullable_to_non_nullable
              as String?,
      damage_flag: freezed == damage_flag
          ? _value.damage_flag
          : damage_flag // ignore: cast_nullable_to_non_nullable
              as String?,
      other_flag: freezed == other_flag
          ? _value.other_flag
          : other_flag // ignore: cast_nullable_to_non_nullable
              as String?,
      other_riyu: freezed == other_riyu
          ? _value.other_riyu
          : other_riyu // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      license: freezed == license
          ? _value.license
          : license // ignore: cast_nullable_to_non_nullable
              as String?,
      reasonTitle: freezed == reasonTitle
          ? _value.reasonTitle
          : reasonTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      place: freezed == place
          ? _value.place
          : place // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ReRequestImplCopyWith<$Res>
    implements $ReRequestCopyWith<$Res> {
  factory _$$ReRequestImplCopyWith(
          _$ReRequestImpl value, $Res Function(_$ReRequestImpl) then) =
      __$$ReRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? uketsuke_id,
      String? name_sei,
      String? name_mei,
      String? name_name_kana,
      String? old_name,
      String? old_name_kana,
      String? birth_day,
      String? zip_code,
      String? addr1,
      String? addr2,
      String? tel_no,
      String? mail_addr,
      String? kosyu_type,
      String? jimusyo_code,
      String? lost_flag,
      String? change_name_flag,
      String? damage_flag,
      String? other_flag,
      String? other_riyu,
      String? comment,
      String? url,
      String? license,
      String? reasonTitle,
      String? place});
}

/// @nodoc
class __$$ReRequestImplCopyWithImpl<$Res>
    extends _$ReRequestCopyWithImpl<$Res, _$ReRequestImpl>
    implements _$$ReRequestImplCopyWith<$Res> {
  __$$ReRequestImplCopyWithImpl(
      _$ReRequestImpl _value, $Res Function(_$ReRequestImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uketsuke_id = freezed,
    Object? name_sei = freezed,
    Object? name_mei = freezed,
    Object? name_name_kana = freezed,
    Object? old_name = freezed,
    Object? old_name_kana = freezed,
    Object? birth_day = freezed,
    Object? zip_code = freezed,
    Object? addr1 = freezed,
    Object? addr2 = freezed,
    Object? tel_no = freezed,
    Object? mail_addr = freezed,
    Object? kosyu_type = freezed,
    Object? jimusyo_code = freezed,
    Object? lost_flag = freezed,
    Object? change_name_flag = freezed,
    Object? damage_flag = freezed,
    Object? other_flag = freezed,
    Object? other_riyu = freezed,
    Object? comment = freezed,
    Object? url = freezed,
    Object? license = freezed,
    Object? reasonTitle = freezed,
    Object? place = freezed,
  }) {
    return _then(_$ReRequestImpl(
      uketsuke_id: freezed == uketsuke_id
          ? _value.uketsuke_id
          : uketsuke_id // ignore: cast_nullable_to_non_nullable
              as String?,
      name_sei: freezed == name_sei
          ? _value.name_sei
          : name_sei // ignore: cast_nullable_to_non_nullable
              as String?,
      name_mei: freezed == name_mei
          ? _value.name_mei
          : name_mei // ignore: cast_nullable_to_non_nullable
              as String?,
      name_name_kana: freezed == name_name_kana
          ? _value.name_name_kana
          : name_name_kana // ignore: cast_nullable_to_non_nullable
              as String?,
      old_name: freezed == old_name
          ? _value.old_name
          : old_name // ignore: cast_nullable_to_non_nullable
              as String?,
      old_name_kana: freezed == old_name_kana
          ? _value.old_name_kana
          : old_name_kana // ignore: cast_nullable_to_non_nullable
              as String?,
      birth_day: freezed == birth_day
          ? _value.birth_day
          : birth_day // ignore: cast_nullable_to_non_nullable
              as String?,
      zip_code: freezed == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String?,
      addr1: freezed == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String?,
      addr2: freezed == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String?,
      tel_no: freezed == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String?,
      mail_addr: freezed == mail_addr
          ? _value.mail_addr
          : mail_addr // ignore: cast_nullable_to_non_nullable
              as String?,
      kosyu_type: freezed == kosyu_type
          ? _value.kosyu_type
          : kosyu_type // ignore: cast_nullable_to_non_nullable
              as String?,
      jimusyo_code: freezed == jimusyo_code
          ? _value.jimusyo_code
          : jimusyo_code // ignore: cast_nullable_to_non_nullable
              as String?,
      lost_flag: freezed == lost_flag
          ? _value.lost_flag
          : lost_flag // ignore: cast_nullable_to_non_nullable
              as String?,
      change_name_flag: freezed == change_name_flag
          ? _value.change_name_flag
          : change_name_flag // ignore: cast_nullable_to_non_nullable
              as String?,
      damage_flag: freezed == damage_flag
          ? _value.damage_flag
          : damage_flag // ignore: cast_nullable_to_non_nullable
              as String?,
      other_flag: freezed == other_flag
          ? _value.other_flag
          : other_flag // ignore: cast_nullable_to_non_nullable
              as String?,
      other_riyu: freezed == other_riyu
          ? _value.other_riyu
          : other_riyu // ignore: cast_nullable_to_non_nullable
              as String?,
      comment: freezed == comment
          ? _value.comment
          : comment // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      license: freezed == license
          ? _value.license
          : license // ignore: cast_nullable_to_non_nullable
              as String?,
      reasonTitle: freezed == reasonTitle
          ? _value.reasonTitle
          : reasonTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      place: freezed == place
          ? _value.place
          : place // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReRequestImpl implements _ReRequest {
  const _$ReRequestImpl(
      {this.uketsuke_id,
      this.name_sei,
      this.name_mei,
      this.name_name_kana,
      this.old_name,
      this.old_name_kana,
      this.birth_day,
      this.zip_code,
      this.addr1,
      this.addr2,
      this.tel_no,
      this.mail_addr,
      this.kosyu_type,
      this.jimusyo_code,
      this.lost_flag,
      this.change_name_flag,
      this.damage_flag,
      this.other_flag,
      this.other_riyu,
      this.comment,
      this.url,
      this.license,
      this.reasonTitle,
      this.place});

  factory _$ReRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReRequestImplFromJson(json);

  @override
  final String? uketsuke_id;
// 受付ID
  @override
  final String? name_sei;
// 姓
  @override
  final String? name_mei;
// 名
  @override
  final String? name_name_kana;
// 氏名カナ
  @override
  final String? old_name;
// 旧姓
  @override
  final String? old_name_kana;
// 旧姓カナ
  @override
  final String? birth_day;
// 生年月日
  @override
  final String? zip_code;
// 郵便番号
  @override
  final String? addr1;
// 住所1
  @override
  final String? addr2;
// 住所2
  @override
  final String? tel_no;
// 電話番号
  @override
  final String? mail_addr;
// メールアドレス
  @override
  final String? kosyu_type;
// 講習種類
  @override
  final String? jimusyo_code;
// 事務所コード
  @override
  final String? lost_flag;
// 紛失フラグ
  @override
  final String? change_name_flag;
// 氏名変更フラグ
  @override
  final String? damage_flag;
// 破損汚れフラグ
  @override
  final String? other_flag;
// その他フラグ
  @override
  final String? other_riyu;
// その他理由
  @override
  final String? comment;
// 連絡事項
  @override
  final String? url;
// 入力フォームアクセス用URL
// ---- Tmp use ---------
  @override
  final String? license;
  @override
  final String? reasonTitle;
  @override
  final String? place;

  @override
  String toString() {
    return 'ReRequest(uketsuke_id: $uketsuke_id, name_sei: $name_sei, name_mei: $name_mei, name_name_kana: $name_name_kana, old_name: $old_name, old_name_kana: $old_name_kana, birth_day: $birth_day, zip_code: $zip_code, addr1: $addr1, addr2: $addr2, tel_no: $tel_no, mail_addr: $mail_addr, kosyu_type: $kosyu_type, jimusyo_code: $jimusyo_code, lost_flag: $lost_flag, change_name_flag: $change_name_flag, damage_flag: $damage_flag, other_flag: $other_flag, other_riyu: $other_riyu, comment: $comment, url: $url, license: $license, reasonTitle: $reasonTitle, place: $place)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReRequestImpl &&
            (identical(other.uketsuke_id, uketsuke_id) ||
                other.uketsuke_id == uketsuke_id) &&
            (identical(other.name_sei, name_sei) ||
                other.name_sei == name_sei) &&
            (identical(other.name_mei, name_mei) ||
                other.name_mei == name_mei) &&
            (identical(other.name_name_kana, name_name_kana) ||
                other.name_name_kana == name_name_kana) &&
            (identical(other.old_name, old_name) ||
                other.old_name == old_name) &&
            (identical(other.old_name_kana, old_name_kana) ||
                other.old_name_kana == old_name_kana) &&
            (identical(other.birth_day, birth_day) ||
                other.birth_day == birth_day) &&
            (identical(other.zip_code, zip_code) ||
                other.zip_code == zip_code) &&
            (identical(other.addr1, addr1) || other.addr1 == addr1) &&
            (identical(other.addr2, addr2) || other.addr2 == addr2) &&
            (identical(other.tel_no, tel_no) || other.tel_no == tel_no) &&
            (identical(other.mail_addr, mail_addr) ||
                other.mail_addr == mail_addr) &&
            (identical(other.kosyu_type, kosyu_type) ||
                other.kosyu_type == kosyu_type) &&
            (identical(other.jimusyo_code, jimusyo_code) ||
                other.jimusyo_code == jimusyo_code) &&
            (identical(other.lost_flag, lost_flag) ||
                other.lost_flag == lost_flag) &&
            (identical(other.change_name_flag, change_name_flag) ||
                other.change_name_flag == change_name_flag) &&
            (identical(other.damage_flag, damage_flag) ||
                other.damage_flag == damage_flag) &&
            (identical(other.other_flag, other_flag) ||
                other.other_flag == other_flag) &&
            (identical(other.other_riyu, other_riyu) ||
                other.other_riyu == other_riyu) &&
            (identical(other.comment, comment) || other.comment == comment) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.license, license) || other.license == license) &&
            (identical(other.reasonTitle, reasonTitle) ||
                other.reasonTitle == reasonTitle) &&
            (identical(other.place, place) || other.place == place));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        uketsuke_id,
        name_sei,
        name_mei,
        name_name_kana,
        old_name,
        old_name_kana,
        birth_day,
        zip_code,
        addr1,
        addr2,
        tel_no,
        mail_addr,
        kosyu_type,
        jimusyo_code,
        lost_flag,
        change_name_flag,
        damage_flag,
        other_flag,
        other_riyu,
        comment,
        url,
        license,
        reasonTitle,
        place
      ]);

  /// Create a copy of ReRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReRequestImplCopyWith<_$ReRequestImpl> get copyWith =>
      __$$ReRequestImplCopyWithImpl<_$ReRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReRequestImplToJson(
      this,
    );
  }
}

abstract class _ReRequest implements ReRequest {
  const factory _ReRequest(
      {final String? uketsuke_id,
      final String? name_sei,
      final String? name_mei,
      final String? name_name_kana,
      final String? old_name,
      final String? old_name_kana,
      final String? birth_day,
      final String? zip_code,
      final String? addr1,
      final String? addr2,
      final String? tel_no,
      final String? mail_addr,
      final String? kosyu_type,
      final String? jimusyo_code,
      final String? lost_flag,
      final String? change_name_flag,
      final String? damage_flag,
      final String? other_flag,
      final String? other_riyu,
      final String? comment,
      final String? url,
      final String? license,
      final String? reasonTitle,
      final String? place}) = _$ReRequestImpl;

  factory _ReRequest.fromJson(Map<String, dynamic> json) =
      _$ReRequestImpl.fromJson;

  @override
  String? get uketsuke_id; // 受付ID
  @override
  String? get name_sei; // 姓
  @override
  String? get name_mei; // 名
  @override
  String? get name_name_kana; // 氏名カナ
  @override
  String? get old_name; // 旧姓
  @override
  String? get old_name_kana; // 旧姓カナ
  @override
  String? get birth_day; // 生年月日
  @override
  String? get zip_code; // 郵便番号
  @override
  String? get addr1; // 住所1
  @override
  String? get addr2; // 住所2
  @override
  String? get tel_no; // 電話番号
  @override
  String? get mail_addr; // メールアドレス
  @override
  String? get kosyu_type; // 講習種類
  @override
  String? get jimusyo_code; // 事務所コード
  @override
  String? get lost_flag; // 紛失フラグ
  @override
  String? get change_name_flag; // 氏名変更フラグ
  @override
  String? get damage_flag; // 破損汚れフラグ
  @override
  String? get other_flag; // その他フラグ
  @override
  String? get other_riyu; // その他理由
  @override
  String? get comment; // 連絡事項
  @override
  String? get url; // 入力フォームアクセス用URL
// ---- Tmp use ---------
  @override
  String? get license;
  @override
  String? get reasonTitle;
  @override
  String? get place;

  /// Create a copy of ReRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReRequestImplCopyWith<_$ReRequestImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ReRequestDoc _$ReRequestDocFromJson(Map<String, dynamic> json) {
  return _ReRequestDoc.fromJson(json);
}

/// @nodoc
mixin _$ReRequestDoc {
  String? get uketsuke_id => throw _privateConstructorUsedError; // 受付ID
  String? get name => throw _privateConstructorUsedError; // 名
  String? get name_kana => throw _privateConstructorUsedError; // 氏名カナ
  String? get birth_day => throw _privateConstructorUsedError; // 生年月日
  String? get zip_code => throw _privateConstructorUsedError; // 郵便番号
  String? get addr1 => throw _privateConstructorUsedError; // 住所1
  String? get addr2 => throw _privateConstructorUsedError; // 住所2
  String? get tel_no => throw _privateConstructorUsedError; // 電話番号
  String? get mail_addr => throw _privateConstructorUsedError; // メールアドレス
  String? get soufu_name => throw _privateConstructorUsedError; // 送付先名
  String? get soufu_zip_code => throw _privateConstructorUsedError; // 送付先郵便番号
  String? get soufu_addr1 => throw _privateConstructorUsedError; // 送付先住所1
  String? get soufu_addr2 => throw _privateConstructorUsedError; // 送付先住所2
  String? get image_photo =>
      throw _privateConstructorUsedError; // 本人写真画像データ(Base64)
  String? get filename_photo => throw _privateConstructorUsedError; // 本人写真ファイル名
  String? get filetype_photo =>
      throw _privateConstructorUsedError; // 本人写真ファイル形式(MIMEType)
  String? get image_kakunin_omote =>
      throw _privateConstructorUsedError; // 本人確認(表)画像データ(Base64)
  String? get filename_kakunin_omote =>
      throw _privateConstructorUsedError; // 本人確認(表)ファイル名
  String? get filetype_kakunin_omote =>
      throw _privateConstructorUsedError; // 本人確認(表)ファイル形式(MIMEType)
  String? get image_kakunin_ura =>
      throw _privateConstructorUsedError; // 本人確認(裏)画像データ(Base64)
  String? get filename_kakunin_ura =>
      throw _privateConstructorUsedError; // 本人確認(裏)ファイル名
  String? get filetype_kakunin_ura =>
      throw _privateConstructorUsedError; // 本人確認(裏)ファイル形式(MIMEType)
  String? get image_kakunin_atsumi =>
      throw _privateConstructorUsedError; // 本人確認(厚み)画像データ(Base64)
  String? get filename_kakunin_atsumi =>
      throw _privateConstructorUsedError; // 本人確認(厚み)ファイル名
  String? get filetype_kakunin_atsumi =>
      throw _privateConstructorUsedError; // 本人確認(厚み)ファイル形式(MIMEType)
  String? get image_kakunin_rename =>
      throw _privateConstructorUsedError; // 旧姓通称確認画像データ(Base64)
  String? get filename_kakunin_rename =>
      throw _privateConstructorUsedError; // 旧姓通称確認ファイル名
  String? get filetype_kakunin_rename =>
      throw _privateConstructorUsedError; // 旧姓通称確認ファイル形式(MIMEType)
  String? get url => throw _privateConstructorUsedError; // 入力フォームアクセス用URL
  ReRequest? get irai_data => throw _privateConstructorUsedError;

  /// Serializes this ReRequestDoc to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ReRequestDoc
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ReRequestDocCopyWith<ReRequestDoc> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ReRequestDocCopyWith<$Res> {
  factory $ReRequestDocCopyWith(
          ReRequestDoc value, $Res Function(ReRequestDoc) then) =
      _$ReRequestDocCopyWithImpl<$Res, ReRequestDoc>;
  @useResult
  $Res call(
      {String? uketsuke_id,
      String? name,
      String? name_kana,
      String? birth_day,
      String? zip_code,
      String? addr1,
      String? addr2,
      String? tel_no,
      String? mail_addr,
      String? soufu_name,
      String? soufu_zip_code,
      String? soufu_addr1,
      String? soufu_addr2,
      String? image_photo,
      String? filename_photo,
      String? filetype_photo,
      String? image_kakunin_omote,
      String? filename_kakunin_omote,
      String? filetype_kakunin_omote,
      String? image_kakunin_ura,
      String? filename_kakunin_ura,
      String? filetype_kakunin_ura,
      String? image_kakunin_atsumi,
      String? filename_kakunin_atsumi,
      String? filetype_kakunin_atsumi,
      String? image_kakunin_rename,
      String? filename_kakunin_rename,
      String? filetype_kakunin_rename,
      String? url,
      ReRequest? irai_data});

  $ReRequestCopyWith<$Res>? get irai_data;
}

/// @nodoc
class _$ReRequestDocCopyWithImpl<$Res, $Val extends ReRequestDoc>
    implements $ReRequestDocCopyWith<$Res> {
  _$ReRequestDocCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ReRequestDoc
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uketsuke_id = freezed,
    Object? name = freezed,
    Object? name_kana = freezed,
    Object? birth_day = freezed,
    Object? zip_code = freezed,
    Object? addr1 = freezed,
    Object? addr2 = freezed,
    Object? tel_no = freezed,
    Object? mail_addr = freezed,
    Object? soufu_name = freezed,
    Object? soufu_zip_code = freezed,
    Object? soufu_addr1 = freezed,
    Object? soufu_addr2 = freezed,
    Object? image_photo = freezed,
    Object? filename_photo = freezed,
    Object? filetype_photo = freezed,
    Object? image_kakunin_omote = freezed,
    Object? filename_kakunin_omote = freezed,
    Object? filetype_kakunin_omote = freezed,
    Object? image_kakunin_ura = freezed,
    Object? filename_kakunin_ura = freezed,
    Object? filetype_kakunin_ura = freezed,
    Object? image_kakunin_atsumi = freezed,
    Object? filename_kakunin_atsumi = freezed,
    Object? filetype_kakunin_atsumi = freezed,
    Object? image_kakunin_rename = freezed,
    Object? filename_kakunin_rename = freezed,
    Object? filetype_kakunin_rename = freezed,
    Object? url = freezed,
    Object? irai_data = freezed,
  }) {
    return _then(_value.copyWith(
      uketsuke_id: freezed == uketsuke_id
          ? _value.uketsuke_id
          : uketsuke_id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      name_kana: freezed == name_kana
          ? _value.name_kana
          : name_kana // ignore: cast_nullable_to_non_nullable
              as String?,
      birth_day: freezed == birth_day
          ? _value.birth_day
          : birth_day // ignore: cast_nullable_to_non_nullable
              as String?,
      zip_code: freezed == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String?,
      addr1: freezed == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String?,
      addr2: freezed == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String?,
      tel_no: freezed == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String?,
      mail_addr: freezed == mail_addr
          ? _value.mail_addr
          : mail_addr // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_name: freezed == soufu_name
          ? _value.soufu_name
          : soufu_name // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_zip_code: freezed == soufu_zip_code
          ? _value.soufu_zip_code
          : soufu_zip_code // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_addr1: freezed == soufu_addr1
          ? _value.soufu_addr1
          : soufu_addr1 // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_addr2: freezed == soufu_addr2
          ? _value.soufu_addr2
          : soufu_addr2 // ignore: cast_nullable_to_non_nullable
              as String?,
      image_photo: freezed == image_photo
          ? _value.image_photo
          : image_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_photo: freezed == filename_photo
          ? _value.filename_photo
          : filename_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_photo: freezed == filetype_photo
          ? _value.filetype_photo
          : filetype_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_omote: freezed == image_kakunin_omote
          ? _value.image_kakunin_omote
          : image_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_omote: freezed == filename_kakunin_omote
          ? _value.filename_kakunin_omote
          : filename_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_omote: freezed == filetype_kakunin_omote
          ? _value.filetype_kakunin_omote
          : filetype_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_ura: freezed == image_kakunin_ura
          ? _value.image_kakunin_ura
          : image_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_ura: freezed == filename_kakunin_ura
          ? _value.filename_kakunin_ura
          : filename_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_ura: freezed == filetype_kakunin_ura
          ? _value.filetype_kakunin_ura
          : filetype_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_atsumi: freezed == image_kakunin_atsumi
          ? _value.image_kakunin_atsumi
          : image_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_atsumi: freezed == filename_kakunin_atsumi
          ? _value.filename_kakunin_atsumi
          : filename_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_atsumi: freezed == filetype_kakunin_atsumi
          ? _value.filetype_kakunin_atsumi
          : filetype_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_rename: freezed == image_kakunin_rename
          ? _value.image_kakunin_rename
          : image_kakunin_rename // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_rename: freezed == filename_kakunin_rename
          ? _value.filename_kakunin_rename
          : filename_kakunin_rename // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_rename: freezed == filetype_kakunin_rename
          ? _value.filetype_kakunin_rename
          : filetype_kakunin_rename // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      irai_data: freezed == irai_data
          ? _value.irai_data
          : irai_data // ignore: cast_nullable_to_non_nullable
              as ReRequest?,
    ) as $Val);
  }

  /// Create a copy of ReRequestDoc
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ReRequestCopyWith<$Res>? get irai_data {
    if (_value.irai_data == null) {
      return null;
    }

    return $ReRequestCopyWith<$Res>(_value.irai_data!, (value) {
      return _then(_value.copyWith(irai_data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ReRequestDocImplCopyWith<$Res>
    implements $ReRequestDocCopyWith<$Res> {
  factory _$$ReRequestDocImplCopyWith(
          _$ReRequestDocImpl value, $Res Function(_$ReRequestDocImpl) then) =
      __$$ReRequestDocImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? uketsuke_id,
      String? name,
      String? name_kana,
      String? birth_day,
      String? zip_code,
      String? addr1,
      String? addr2,
      String? tel_no,
      String? mail_addr,
      String? soufu_name,
      String? soufu_zip_code,
      String? soufu_addr1,
      String? soufu_addr2,
      String? image_photo,
      String? filename_photo,
      String? filetype_photo,
      String? image_kakunin_omote,
      String? filename_kakunin_omote,
      String? filetype_kakunin_omote,
      String? image_kakunin_ura,
      String? filename_kakunin_ura,
      String? filetype_kakunin_ura,
      String? image_kakunin_atsumi,
      String? filename_kakunin_atsumi,
      String? filetype_kakunin_atsumi,
      String? image_kakunin_rename,
      String? filename_kakunin_rename,
      String? filetype_kakunin_rename,
      String? url,
      ReRequest? irai_data});

  @override
  $ReRequestCopyWith<$Res>? get irai_data;
}

/// @nodoc
class __$$ReRequestDocImplCopyWithImpl<$Res>
    extends _$ReRequestDocCopyWithImpl<$Res, _$ReRequestDocImpl>
    implements _$$ReRequestDocImplCopyWith<$Res> {
  __$$ReRequestDocImplCopyWithImpl(
      _$ReRequestDocImpl _value, $Res Function(_$ReRequestDocImpl) _then)
      : super(_value, _then);

  /// Create a copy of ReRequestDoc
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? uketsuke_id = freezed,
    Object? name = freezed,
    Object? name_kana = freezed,
    Object? birth_day = freezed,
    Object? zip_code = freezed,
    Object? addr1 = freezed,
    Object? addr2 = freezed,
    Object? tel_no = freezed,
    Object? mail_addr = freezed,
    Object? soufu_name = freezed,
    Object? soufu_zip_code = freezed,
    Object? soufu_addr1 = freezed,
    Object? soufu_addr2 = freezed,
    Object? image_photo = freezed,
    Object? filename_photo = freezed,
    Object? filetype_photo = freezed,
    Object? image_kakunin_omote = freezed,
    Object? filename_kakunin_omote = freezed,
    Object? filetype_kakunin_omote = freezed,
    Object? image_kakunin_ura = freezed,
    Object? filename_kakunin_ura = freezed,
    Object? filetype_kakunin_ura = freezed,
    Object? image_kakunin_atsumi = freezed,
    Object? filename_kakunin_atsumi = freezed,
    Object? filetype_kakunin_atsumi = freezed,
    Object? image_kakunin_rename = freezed,
    Object? filename_kakunin_rename = freezed,
    Object? filetype_kakunin_rename = freezed,
    Object? url = freezed,
    Object? irai_data = freezed,
  }) {
    return _then(_$ReRequestDocImpl(
      uketsuke_id: freezed == uketsuke_id
          ? _value.uketsuke_id
          : uketsuke_id // ignore: cast_nullable_to_non_nullable
              as String?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      name_kana: freezed == name_kana
          ? _value.name_kana
          : name_kana // ignore: cast_nullable_to_non_nullable
              as String?,
      birth_day: freezed == birth_day
          ? _value.birth_day
          : birth_day // ignore: cast_nullable_to_non_nullable
              as String?,
      zip_code: freezed == zip_code
          ? _value.zip_code
          : zip_code // ignore: cast_nullable_to_non_nullable
              as String?,
      addr1: freezed == addr1
          ? _value.addr1
          : addr1 // ignore: cast_nullable_to_non_nullable
              as String?,
      addr2: freezed == addr2
          ? _value.addr2
          : addr2 // ignore: cast_nullable_to_non_nullable
              as String?,
      tel_no: freezed == tel_no
          ? _value.tel_no
          : tel_no // ignore: cast_nullable_to_non_nullable
              as String?,
      mail_addr: freezed == mail_addr
          ? _value.mail_addr
          : mail_addr // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_name: freezed == soufu_name
          ? _value.soufu_name
          : soufu_name // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_zip_code: freezed == soufu_zip_code
          ? _value.soufu_zip_code
          : soufu_zip_code // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_addr1: freezed == soufu_addr1
          ? _value.soufu_addr1
          : soufu_addr1 // ignore: cast_nullable_to_non_nullable
              as String?,
      soufu_addr2: freezed == soufu_addr2
          ? _value.soufu_addr2
          : soufu_addr2 // ignore: cast_nullable_to_non_nullable
              as String?,
      image_photo: freezed == image_photo
          ? _value.image_photo
          : image_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_photo: freezed == filename_photo
          ? _value.filename_photo
          : filename_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_photo: freezed == filetype_photo
          ? _value.filetype_photo
          : filetype_photo // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_omote: freezed == image_kakunin_omote
          ? _value.image_kakunin_omote
          : image_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_omote: freezed == filename_kakunin_omote
          ? _value.filename_kakunin_omote
          : filename_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_omote: freezed == filetype_kakunin_omote
          ? _value.filetype_kakunin_omote
          : filetype_kakunin_omote // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_ura: freezed == image_kakunin_ura
          ? _value.image_kakunin_ura
          : image_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_ura: freezed == filename_kakunin_ura
          ? _value.filename_kakunin_ura
          : filename_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_ura: freezed == filetype_kakunin_ura
          ? _value.filetype_kakunin_ura
          : filetype_kakunin_ura // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_atsumi: freezed == image_kakunin_atsumi
          ? _value.image_kakunin_atsumi
          : image_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_atsumi: freezed == filename_kakunin_atsumi
          ? _value.filename_kakunin_atsumi
          : filename_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_atsumi: freezed == filetype_kakunin_atsumi
          ? _value.filetype_kakunin_atsumi
          : filetype_kakunin_atsumi // ignore: cast_nullable_to_non_nullable
              as String?,
      image_kakunin_rename: freezed == image_kakunin_rename
          ? _value.image_kakunin_rename
          : image_kakunin_rename // ignore: cast_nullable_to_non_nullable
              as String?,
      filename_kakunin_rename: freezed == filename_kakunin_rename
          ? _value.filename_kakunin_rename
          : filename_kakunin_rename // ignore: cast_nullable_to_non_nullable
              as String?,
      filetype_kakunin_rename: freezed == filetype_kakunin_rename
          ? _value.filetype_kakunin_rename
          : filetype_kakunin_rename // ignore: cast_nullable_to_non_nullable
              as String?,
      url: freezed == url
          ? _value.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      irai_data: freezed == irai_data
          ? _value.irai_data
          : irai_data // ignore: cast_nullable_to_non_nullable
              as ReRequest?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ReRequestDocImpl implements _ReRequestDoc {
  const _$ReRequestDocImpl(
      {this.uketsuke_id,
      this.name,
      this.name_kana,
      this.birth_day,
      this.zip_code,
      this.addr1,
      this.addr2,
      this.tel_no,
      this.mail_addr,
      this.soufu_name,
      this.soufu_zip_code,
      this.soufu_addr1,
      this.soufu_addr2,
      this.image_photo,
      this.filename_photo,
      this.filetype_photo,
      this.image_kakunin_omote,
      this.filename_kakunin_omote,
      this.filetype_kakunin_omote,
      this.image_kakunin_ura,
      this.filename_kakunin_ura,
      this.filetype_kakunin_ura,
      this.image_kakunin_atsumi,
      this.filename_kakunin_atsumi,
      this.filetype_kakunin_atsumi,
      this.image_kakunin_rename,
      this.filename_kakunin_rename,
      this.filetype_kakunin_rename,
      this.url,
      this.irai_data});

  factory _$ReRequestDocImpl.fromJson(Map<String, dynamic> json) =>
      _$$ReRequestDocImplFromJson(json);

  @override
  final String? uketsuke_id;
// 受付ID
  @override
  final String? name;
// 名
  @override
  final String? name_kana;
// 氏名カナ
  @override
  final String? birth_day;
// 生年月日
  @override
  final String? zip_code;
// 郵便番号
  @override
  final String? addr1;
// 住所1
  @override
  final String? addr2;
// 住所2
  @override
  final String? tel_no;
// 電話番号
  @override
  final String? mail_addr;
// メールアドレス
  @override
  final String? soufu_name;
// 送付先名
  @override
  final String? soufu_zip_code;
// 送付先郵便番号
  @override
  final String? soufu_addr1;
// 送付先住所1
  @override
  final String? soufu_addr2;
// 送付先住所2
  @override
  final String? image_photo;
// 本人写真画像データ(Base64)
  @override
  final String? filename_photo;
// 本人写真ファイル名
  @override
  final String? filetype_photo;
// 本人写真ファイル形式(MIMEType)
  @override
  final String? image_kakunin_omote;
// 本人確認(表)画像データ(Base64)
  @override
  final String? filename_kakunin_omote;
// 本人確認(表)ファイル名
  @override
  final String? filetype_kakunin_omote;
// 本人確認(表)ファイル形式(MIMEType)
  @override
  final String? image_kakunin_ura;
// 本人確認(裏)画像データ(Base64)
  @override
  final String? filename_kakunin_ura;
// 本人確認(裏)ファイル名
  @override
  final String? filetype_kakunin_ura;
// 本人確認(裏)ファイル形式(MIMEType)
  @override
  final String? image_kakunin_atsumi;
// 本人確認(厚み)画像データ(Base64)
  @override
  final String? filename_kakunin_atsumi;
// 本人確認(厚み)ファイル名
  @override
  final String? filetype_kakunin_atsumi;
// 本人確認(厚み)ファイル形式(MIMEType)
  @override
  final String? image_kakunin_rename;
// 旧姓通称確認画像データ(Base64)
  @override
  final String? filename_kakunin_rename;
// 旧姓通称確認ファイル名
  @override
  final String? filetype_kakunin_rename;
// 旧姓通称確認ファイル形式(MIMEType)
  @override
  final String? url;
// 入力フォームアクセス用URL
  @override
  final ReRequest? irai_data;

  @override
  String toString() {
    return 'ReRequestDoc(uketsuke_id: $uketsuke_id, name: $name, name_kana: $name_kana, birth_day: $birth_day, zip_code: $zip_code, addr1: $addr1, addr2: $addr2, tel_no: $tel_no, mail_addr: $mail_addr, soufu_name: $soufu_name, soufu_zip_code: $soufu_zip_code, soufu_addr1: $soufu_addr1, soufu_addr2: $soufu_addr2, image_photo: $image_photo, filename_photo: $filename_photo, filetype_photo: $filetype_photo, image_kakunin_omote: $image_kakunin_omote, filename_kakunin_omote: $filename_kakunin_omote, filetype_kakunin_omote: $filetype_kakunin_omote, image_kakunin_ura: $image_kakunin_ura, filename_kakunin_ura: $filename_kakunin_ura, filetype_kakunin_ura: $filetype_kakunin_ura, image_kakunin_atsumi: $image_kakunin_atsumi, filename_kakunin_atsumi: $filename_kakunin_atsumi, filetype_kakunin_atsumi: $filetype_kakunin_atsumi, image_kakunin_rename: $image_kakunin_rename, filename_kakunin_rename: $filename_kakunin_rename, filetype_kakunin_rename: $filetype_kakunin_rename, url: $url, irai_data: $irai_data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ReRequestDocImpl &&
            (identical(other.uketsuke_id, uketsuke_id) ||
                other.uketsuke_id == uketsuke_id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.name_kana, name_kana) ||
                other.name_kana == name_kana) &&
            (identical(other.birth_day, birth_day) ||
                other.birth_day == birth_day) &&
            (identical(other.zip_code, zip_code) ||
                other.zip_code == zip_code) &&
            (identical(other.addr1, addr1) || other.addr1 == addr1) &&
            (identical(other.addr2, addr2) || other.addr2 == addr2) &&
            (identical(other.tel_no, tel_no) || other.tel_no == tel_no) &&
            (identical(other.mail_addr, mail_addr) ||
                other.mail_addr == mail_addr) &&
            (identical(other.soufu_name, soufu_name) ||
                other.soufu_name == soufu_name) &&
            (identical(other.soufu_zip_code, soufu_zip_code) ||
                other.soufu_zip_code == soufu_zip_code) &&
            (identical(other.soufu_addr1, soufu_addr1) ||
                other.soufu_addr1 == soufu_addr1) &&
            (identical(other.soufu_addr2, soufu_addr2) ||
                other.soufu_addr2 == soufu_addr2) &&
            (identical(other.image_photo, image_photo) ||
                other.image_photo == image_photo) &&
            (identical(other.filename_photo, filename_photo) ||
                other.filename_photo == filename_photo) &&
            (identical(other.filetype_photo, filetype_photo) ||
                other.filetype_photo == filetype_photo) &&
            (identical(other.image_kakunin_omote, image_kakunin_omote) ||
                other.image_kakunin_omote == image_kakunin_omote) &&
            (identical(other.filename_kakunin_omote, filename_kakunin_omote) ||
                other.filename_kakunin_omote == filename_kakunin_omote) &&
            (identical(other.filetype_kakunin_omote, filetype_kakunin_omote) ||
                other.filetype_kakunin_omote == filetype_kakunin_omote) &&
            (identical(other.image_kakunin_ura, image_kakunin_ura) ||
                other.image_kakunin_ura == image_kakunin_ura) &&
            (identical(other.filename_kakunin_ura, filename_kakunin_ura) ||
                other.filename_kakunin_ura == filename_kakunin_ura) &&
            (identical(other.filetype_kakunin_ura, filetype_kakunin_ura) ||
                other.filetype_kakunin_ura == filetype_kakunin_ura) &&
            (identical(other.image_kakunin_atsumi, image_kakunin_atsumi) ||
                other.image_kakunin_atsumi == image_kakunin_atsumi) &&
            (identical(
                    other.filename_kakunin_atsumi, filename_kakunin_atsumi) ||
                other.filename_kakunin_atsumi == filename_kakunin_atsumi) &&
            (identical(
                    other.filetype_kakunin_atsumi, filetype_kakunin_atsumi) ||
                other.filetype_kakunin_atsumi == filetype_kakunin_atsumi) &&
            (identical(other.image_kakunin_rename, image_kakunin_rename) ||
                other.image_kakunin_rename == image_kakunin_rename) &&
            (identical(
                    other.filename_kakunin_rename, filename_kakunin_rename) ||
                other.filename_kakunin_rename == filename_kakunin_rename) &&
            (identical(
                    other.filetype_kakunin_rename, filetype_kakunin_rename) ||
                other.filetype_kakunin_rename == filetype_kakunin_rename) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.irai_data, irai_data) ||
                other.irai_data == irai_data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        uketsuke_id,
        name,
        name_kana,
        birth_day,
        zip_code,
        addr1,
        addr2,
        tel_no,
        mail_addr,
        soufu_name,
        soufu_zip_code,
        soufu_addr1,
        soufu_addr2,
        image_photo,
        filename_photo,
        filetype_photo,
        image_kakunin_omote,
        filename_kakunin_omote,
        filetype_kakunin_omote,
        image_kakunin_ura,
        filename_kakunin_ura,
        filetype_kakunin_ura,
        image_kakunin_atsumi,
        filename_kakunin_atsumi,
        filetype_kakunin_atsumi,
        image_kakunin_rename,
        filename_kakunin_rename,
        filetype_kakunin_rename,
        url,
        irai_data
      ]);

  /// Create a copy of ReRequestDoc
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ReRequestDocImplCopyWith<_$ReRequestDocImpl> get copyWith =>
      __$$ReRequestDocImplCopyWithImpl<_$ReRequestDocImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ReRequestDocImplToJson(
      this,
    );
  }
}

abstract class _ReRequestDoc implements ReRequestDoc {
  const factory _ReRequestDoc(
      {final String? uketsuke_id,
      final String? name,
      final String? name_kana,
      final String? birth_day,
      final String? zip_code,
      final String? addr1,
      final String? addr2,
      final String? tel_no,
      final String? mail_addr,
      final String? soufu_name,
      final String? soufu_zip_code,
      final String? soufu_addr1,
      final String? soufu_addr2,
      final String? image_photo,
      final String? filename_photo,
      final String? filetype_photo,
      final String? image_kakunin_omote,
      final String? filename_kakunin_omote,
      final String? filetype_kakunin_omote,
      final String? image_kakunin_ura,
      final String? filename_kakunin_ura,
      final String? filetype_kakunin_ura,
      final String? image_kakunin_atsumi,
      final String? filename_kakunin_atsumi,
      final String? filetype_kakunin_atsumi,
      final String? image_kakunin_rename,
      final String? filename_kakunin_rename,
      final String? filetype_kakunin_rename,
      final String? url,
      final ReRequest? irai_data}) = _$ReRequestDocImpl;

  factory _ReRequestDoc.fromJson(Map<String, dynamic> json) =
      _$ReRequestDocImpl.fromJson;

  @override
  String? get uketsuke_id; // 受付ID
  @override
  String? get name; // 名
  @override
  String? get name_kana; // 氏名カナ
  @override
  String? get birth_day; // 生年月日
  @override
  String? get zip_code; // 郵便番号
  @override
  String? get addr1; // 住所1
  @override
  String? get addr2; // 住所2
  @override
  String? get tel_no; // 電話番号
  @override
  String? get mail_addr; // メールアドレス
  @override
  String? get soufu_name; // 送付先名
  @override
  String? get soufu_zip_code; // 送付先郵便番号
  @override
  String? get soufu_addr1; // 送付先住所1
  @override
  String? get soufu_addr2; // 送付先住所2
  @override
  String? get image_photo; // 本人写真画像データ(Base64)
  @override
  String? get filename_photo; // 本人写真ファイル名
  @override
  String? get filetype_photo; // 本人写真ファイル形式(MIMEType)
  @override
  String? get image_kakunin_omote; // 本人確認(表)画像データ(Base64)
  @override
  String? get filename_kakunin_omote; // 本人確認(表)ファイル名
  @override
  String? get filetype_kakunin_omote; // 本人確認(表)ファイル形式(MIMEType)
  @override
  String? get image_kakunin_ura; // 本人確認(裏)画像データ(Base64)
  @override
  String? get filename_kakunin_ura; // 本人確認(裏)ファイル名
  @override
  String? get filetype_kakunin_ura; // 本人確認(裏)ファイル形式(MIMEType)
  @override
  String? get image_kakunin_atsumi; // 本人確認(厚み)画像データ(Base64)
  @override
  String? get filename_kakunin_atsumi; // 本人確認(厚み)ファイル名
  @override
  String? get filetype_kakunin_atsumi; // 本人確認(厚み)ファイル形式(MIMEType)
  @override
  String? get image_kakunin_rename; // 旧姓通称確認画像データ(Base64)
  @override
  String? get filename_kakunin_rename; // 旧姓通称確認ファイル名
  @override
  String? get filetype_kakunin_rename; // 旧姓通称確認ファイル形式(MIMEType)
  @override
  String? get url; // 入力フォームアクセス用URL
  @override
  ReRequest? get irai_data;

  /// Create a copy of ReRequestDoc
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ReRequestDocImplCopyWith<_$ReRequestDocImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
