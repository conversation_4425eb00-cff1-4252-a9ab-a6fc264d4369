// ファイル名: main.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'course.freezed.dart';
part 'course.g.dart';

@freezed
class Course with _$Course {
  const factory Course({
    required String kosyu_number, // 講習コード
    required String kosyu_code, // 講習コード
    required String kosyu_name, // 講習名
    required String kosyu_omit, // 講習名略
    required String jimusyo_code, // 事務所コード
    required String jimusyo_name, // 事務所名
    required String kaijyo_code, // 会場コード
    required String kaijyo_name, // 会場名
    required String subtitle, // サブタイトル
    // required String? course_code, // コースコード
    // required String course_name, // コース名
    // required int course_days, // コース日数
    // required int price, // 口数金額
    required int attend_days_count, // 受講日件数
    required List<AttendDay> attend_days_list, // 受講日リスト
  }) = _Course;

  factory Course.fromJson(Map<String, Object?> json) => _$CourseFromJson(json);
}

@freezed
class AttendDay with _$AttendDay {
  const factory AttendDay({
    required String start_date, // 開始日
    required String end_date, // 終了日
    required int request_count, // 申込数
    required int capacity_count, // 定員数
    required int suspended_flag, // 中止フラグ
    required String course_code, // コースコード
    required String course_name, // コース名
    required int course_days, // コース日数
    required int price, // 料金
  }) = _AttendDay;

  factory AttendDay.fromJson(Map<String, dynamic> json) =>
      _$AttendDayFromJson(json);
}
