import 'package:flutter/material.dart';

class FooterBox extends StatelessWidget {
  const FooterBox({super.key});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        width: double.infinity, // 幅を画面いっぱいに広げる
        color: Colors.grey[300], // 背景色をグレーに設定
        child: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'ご利用上の注意    個人情報保護への取り組み',
              style: TextStyle(
                color: Colors.black, // テキストカラーを濃い青に設定
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8), // テキスト間にスペースを追加
            Text(
              '特定個人情報等の適正な取扱いに関する基本方針',
              style: TextStyle(
                color: Colors.black, // テキストカラーを濃い青に設定
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            // Container(
            //   height: 4.0, // 下部の境界線の太さ
            //   color: Colors.blue[900], // 境界線の色を濃い青に設定
            // ),
          ],
        ),
      ),
    );
  }
}
