import 'package:flutter/material.dart';

class BottomBar extends StatelessWidget {
  const BottomBar({super.key, required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      height: 40,
      color: Theme.of(context).colorScheme.inversePrimary,
      child: Center(
        child: Text(
          title == '' ? 'Copyright © ボイラ・クレーン安全協会 All Rights Reserved.' : title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}
