import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class UpFabButton extends StatefulWidget {
  const UpFabButton({super.key, required this.scrollViewController});

  final ScrollController scrollViewController;

  @override
  State<UpFabButton> createState() => _UpFabButtonState();
}

class _UpFabButtonState extends State<UpFabButton> {
  bool isVisible = false;

  @override
  void initState() {
    widget.scrollViewController.addListener(() {
      if (widget.scrollViewController.position.userScrollDirection ==
          ScrollDirection.reverse) {
        if (isVisible == false) {
          setState(() {
            isVisible = true;
          });
        }
      } else {
        if (widget.scrollViewController.position.userScrollDirection ==
            ScrollDirection.forward) {
          if (isVisible == true) {
            setState(() {
              isVisible = false;
            });
          }
        }
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: isVisible,
      child: FloatingActionButton(
        onPressed: () {
          widget.scrollViewController.animateTo(
              widget.scrollViewController.position.minScrollExtent,
              duration: const Duration(milliseconds: 200),
              curve: Curves.ease);
          setState(() {
            isVisible = false;
          });
        },
        backgroundColor: Colors.green,
        child: const Icon(
          Icons.arrow_upward,
          color: Colors.white,
        ),
      ),
    );
  }
}
