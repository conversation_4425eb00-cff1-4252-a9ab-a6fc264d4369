import 'package:flutter/material.dart';
import 'package:flutter_breadcrumb/flutter_breadcrumb.dart';

class Steps extends StatefulWidget {
  const Steps({super.key, required this.activeIndex});

  final int activeIndex;

  @override
  State<Steps> createState() => _StepsState();
}

class _StepsState extends State<Steps> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: Colors.white,
          boxShadow: const [
            BoxShadow(
                offset: Offset(0, 1), blurRadius: 3, color: Colors.black26)
          ]),
      padding: const EdgeInsets.all(8),
      child: Center(
        child: BreadCrumb.builder(
          itemCount: BreadcrumbConst.breadcrumbs.length,
          builder: (index) => BreadCrumbItem(
            content: Row(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Icon(
                  BreadcrumbConst.breadcrumbsIcon[index],
                  //color: Colors.white,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4.0),
                  child: Text(
                    BreadcrumbConst.breadcrumbs[index],
                    style: TextStyle(
                      fontWeight: index < widget.activeIndex
                          ? FontWeight.bold
                          : FontWeight.normal,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () {},
            //borderRadius: BorderRadius.circular(4),
            // color: Theme.of(context).colorScheme.inversePrimary,
            disableColor: Theme.of(context).colorScheme.inversePrimary,
            splashColor: Theme.of(context).colorScheme.inversePrimary,
            //textColor: Colors.white,
            disabledTextColor: Colors.grey,
            //padding: const EdgeInsets.all(6),
            //margin: const EdgeInsets.symmetric(vertical: 4),
          ),
          divider: const Icon(
            Icons.chevron_right,
            color: Colors.grey,
          ),
          overflow: ScrollableOverflow(),
        ),
      ),
    );
  }
}

class BreadcrumbConst {
  static const List<String> breadcrumbs = [
    'メール確認',
    '講習選択',
    '受講者情報記入',
    '申込書作成',
  ];
  static const List<IconData> breadcrumbsIcon = [
    Icons.email,
    Icons.menu,
    Icons.edit,
    Icons.text_snippet,
  ];
}
