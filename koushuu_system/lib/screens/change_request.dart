import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:koushuu_system/models/rerequest.dart';
import 'package:koushuu_system/repositories/jimusho_repository.dart';
import 'package:koushuu_system/repositories/request_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/error_info.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:koushuu_system/core/constants.dart' as constants;
import 'package:loader_overlay/loader_overlay.dart';

final optProvider = StateProvider<bool>((ref) {
  return false;
});
final optValueProvider = StateProvider<String>((ref) {
  return "1";
});
final jimushoVProvider = StateProvider<String?>((ref) => null);
String getOptName(String ky) {
  String retVal = "";
  switch (ky) {
    case "1":
      retVal = "紛失";
      break;
    case "2":
      retVal = "氏名変更";
      break;
    case "3":
      retVal = "破損・汚れ";
      break;
    case "4":
      retVal = "その他";
      break;
  }
  return retVal;
}

class ChangeRequestPage extends HookConsumerWidget {
  ChangeRequestPage({super.key, required this.title});

  final String title;
  final ScrollController _scrollController = ScrollController();

  final TextEditingController lastNameController =
      TextEditingController(text: '1');
  final TextEditingController firstNameController =
      TextEditingController(text: '2');
  final TextEditingController furiganaController =
      TextEditingController(text: '3');
  final TextEditingController oldLastNameController =
      TextEditingController(text: '4');
  final TextEditingController oldFuriganaController =
      TextEditingController(text: '5');
  final TextEditingController birthdayController =
      TextEditingController(text: '1990/12/12');
  final TextEditingController licenseController =
      TextEditingController(text: '12345678');
  final TextEditingController licensePlaceController =
      TextEditingController(text: '北海道');
  final TextEditingController otherController =
      TextEditingController(text: 'その他情報');
  final TextEditingController reasonController =
      TextEditingController(text: '4');
  final TextEditingController telController =
      TextEditingController(text: '090-0900-0900');
  final TextEditingController emailController =
      TextEditingController(text: '<EMAIL>');
  final TextEditingController reemailController =
      TextEditingController(text: '<EMAIL>');
  final TextEditingController commentController =
      TextEditingController(text: 'コメント情報');

  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    bool selectedOpt = ref.watch(optProvider);
    // double topPos = MediaQuery.of(context).size.height - 100;

    //jimusho
    List<Map<String, dynamic>> lstJimusho = [];
    final fetchJimushoAsync = ref.watch(fetchJimushoListProvider);
    final selectJimushoValue = ref.watch(jimushoVProvider);

    return Scaffold(
        body: FormBuilder(
          key: _formKey,
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                const Row(
                  children: [
                    TopBar(
                      title: '修了者情報・連絡先の入力',
                    ),
                  ],
                ),
                Row(
                  children: [
                    // Form Section
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Column(
                          children: [
                            _buildTextField('lastName', '姓', lastNameController,
                                false, null, true),
                            _buildTextField('firstName', '名',
                                firstNameController, false, null, true),
                            _buildTextField('furigana', 'ふりがな',
                                furiganaController, false, null, true),
                            _buildTextField('oldLastName', '旧姓',
                                oldLastNameController, false, null, false),
                            _buildTextField('oldFurigana', '旧姓ふりがな',
                                oldFuriganaController, false, null, false),
                            _buildTextField('birthday', '生年月日',
                                birthdayController, false, null, true),
                            _buildTextField('license', '申請する修了証',
                                licenseController, false, null, true),
                            // _buildTextField('licensePlace', '取得した事務所',
                            //     licensePlaceController, false, null, true),
                            fetchJimushoAsync.when(
                              data: (val) {
                                // lstJimusho = val;
                                lstJimusho.clear();

                                return FormBuilderDropdown<String>(
                                  name: "licensePlace",
                                  enabled: true,
                                  onChanged: (newValue) {
                                    ref.read(jimushoVProvider.notifier).state =
                                        newValue!;
                                  },
                                  items: val.map((loc) {
                                    lstJimusho.add(loc);
                                    return DropdownMenuItem<String>(
                                      value: loc["jimusyo_code"],
                                      child: Text(loc["jimusyo_name"]),
                                    );
                                  }).toList(),
                                  isExpanded: true,
                                  validator: FormBuilderValidators.compose([
                                    FormBuilderValidators.required(),
                                  ]),
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: Colors.orangeAccent.shade100,
                                    border: const OutlineInputBorder(),
                                    labelText: "取得した事務所",
                                  ),
                                );
                              },
                              loading: () => const Center(
                                  child: CircularProgressIndicator()),
                              error: (e, _) => ErrorInfo(txt: e.toString()),
                            ),
                            Row(
                              children: [
                                Expanded(
                                  child: FormBuilderRadioGroup<String>(
                                    wrapAlignment: WrapAlignment.start,
                                    name: 'reason',
                                    initialValue: "1",
                                    options: const [
                                      FormBuilderFieldOption(
                                          value: '1', child: Text('紛失')),
                                      FormBuilderFieldOption(
                                          value: '2', child: Text('氏名変更')),
                                      FormBuilderFieldOption(
                                          value: '3', child: Text('破損・汚れ')),
                                      FormBuilderFieldOption(
                                          value: '4', child: Text('その他')),
                                    ],
                                    //initialValue: '1',
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return '選択してください。';
                                      }
                                      return null;
                                    }, // 初期選択
                                    onChanged: (val) {
                                      ref
                                          .read(optValueProvider.notifier)
                                          .state = val!;

                                      if (val == '4') {
                                        ref.read(optProvider.notifier).state =
                                            true;
                                      } else {
                                        ref.read(optProvider.notifier).state =
                                            false;
                                        otherController.clear();
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 5.0),
                              child: TextField(
                                enabled: selectedOpt,
                                minLines: 5,
                                maxLines: 20,
                                controller: otherController,
                                decoration: const InputDecoration(
                                  labelText: "その他の理由",
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 20,
                            ),
                            const Divider(),
                            const SizedBox(
                              height: 20,
                            ),
                            _buildTextField('tel', '電話番号', telController, false,
                                null, true),
                            _buildTextField('email', 'メールアドレス', emailController,
                                false, null, true),
                            _buildTextField('reemail', 'メールアドレス（確認）',
                                reemailController, false, null, true),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 5.0),
                              child: TextField(
                                minLines: 5,
                                maxLines: 20,
                                controller: commentController,
                                decoration: const InputDecoration(
                                  labelText: "連絡事項",
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ),
                            const SizedBox(
                              height: 30,
                            ),
                            Center(
                              child: CoreButton(
                                onPressed: () async {
                                  // Form is valid
                                  if (_formKey.currentState?.validate() ??
                                      false) {
                                    context.loaderOverlay.show();
                                    final selectedReason = ref
                                        .read(optValueProvider.notifier)
                                        .state;
                                    ReRequest tmpR = ReRequest(
                                      uketsuke_id: "",
                                      name_sei: lastNameController.text,
                                      name_mei: firstNameController.text,
                                      name_name_kana: furiganaController.text,
                                      old_name: oldLastNameController.text,
                                      old_name_kana: oldFuriganaController.text,
                                      birth_day: birthdayController.text,
                                      zip_code: "",
                                      addr1: "",
                                      addr2: "",
                                      tel_no: telController.text,
                                      mail_addr: emailController.text,
                                      kosyu_type: licenseController.text,
                                      jimusyo_code: selectJimushoValue,
                                      lost_flag:
                                          selectedReason == '1' ? "1" : "0",
                                      change_name_flag:
                                          selectedReason == '2' ? "1" : "0",
                                      damage_flag:
                                          selectedReason == '3' ? "1" : "0",
                                      other_flag:
                                          selectedReason == '4' ? "1" : "0",
                                      other_riyu: otherController.text,
                                      comment: commentController.text,
                                      url: "",
                                      license: licenseController.text,
                                      reasonTitle: getOptName(ref
                                          .read(optValueProvider.notifier)
                                          .state),
                                      place: licensePlaceController.text,
                                    );

                                    final res = await ref
                                        .read(requestRepositoryProvider)
                                        .makeReRequest(tmpR);

                                    context.loaderOverlay.hide();
                                    if (res['status'] !=
                                        constants.API_RES_SUCCESS) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                          content:
                                              Text('再発行リクエスト作成にエラーが発生しました！'),
                                          // behavior: SnackBarBehavior.floating,
                                          // // shape: RoundedRectangleBorder(
                                          // //   borderRadius:
                                          // //       BorderRadius.circular(24),
                                          // // ),
                                          // margin: const EdgeInsets.only(
                                          //     bottom: topPos,
                                          //     right: 20,
                                          //     left: 20),
                                        ),
                                      );
                                    } else {
                                      GoRouter.of(context).go('/changeend');
                                    }
                                  }
                                },
                                title: '再交付依頼',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}

Widget _buildTextField(
    String name,
    String label,
    TextEditingController controller,
    bool isSecure,
    String? Function(String?)? validator,
    bool? isRequired) {
  isRequired ??= false;
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 5.0),
    child: FormBuilderTextField(
      name: name,
      obscureText: isSecure,
      controller: controller,
      validator: isRequired
          ? (value) {
              if (value == null || value.isEmpty) {
                return '$labelを入力してください。';
              }
              return null;
            }
          : validator,
      decoration: InputDecoration(
        filled: isRequired,
        fillColor:
            isRequired ? Colors.orangeAccent.shade100 : Colors.transparent,
        labelText: label,
        border: const OutlineInputBorder(),
      ),
    ),
  );
}

class LabeledCheckbox extends StatelessWidget {
  const LabeledCheckbox({
    super.key,
    required this.label,
    required this.value,
    required this.onChanged,
  });

  final String label;
  final bool value;
  final Function onChanged;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onChanged(!value);
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          Checkbox(
            value: value,
            onChanged: (bool? newValue) {
              onChanged(newValue);
            },
          ),
          Text(label),
        ],
      ),
    );
  }
}
