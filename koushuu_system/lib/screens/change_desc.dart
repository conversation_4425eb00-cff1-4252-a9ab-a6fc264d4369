import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';

class ChangeDescPage extends StatefulWidget {
  const ChangeDescPage({super.key, required this.title});

  final String title;

  @override
  State<ChangeDescPage> createState() => _ChangeDescPageState();
}

class _ChangeDescPageState extends State<ChangeDescPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              const Row(
                children: [
                  TopBar(
                    title: '統合修了証の案内',
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const HtmlWidget(
                            '''
  <p>
    【統合修了証とは】
  </p>
  <br/>
  <p>
  ●統合修了証とは、これまで講習ごとに交付されていた複数の修了証を1枚にまとめた修了証です。<br/>

  ●統合修了証は、次の3種類に分かれます。
   <ol>
      <li>技能講習統合修了証</li>
      <li>特別教育統合修了証</li>
      <li>安全衛生教育・安全教育統合修了証</li>
    </ol>
  ●統合できる修了証は、当協会で交付された修了証に限ります。<br/>
  ●統合は当協会の事務所にてできます。<br/>
  ●クレジットカードと同じ大きさのプラスチックカードとなります。<br/>
  </p>

  <center><img src="asset:assets/photo_sample3.png"/></center>
  ''',
                          ),
                          const SizedBox(height: 20),
                          Center(
                            child: CoreButton(
                              onPressed: () {
                                GoRouter.of(context).go('/changereq');
                              },
                              title: '次へ',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}
