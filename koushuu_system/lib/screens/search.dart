import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart'; // 日付フォーマット用
import 'package:flutter/material.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/category_list_ui.dart';
import 'package:koushuu_system/widgets/field_title.dart';
import 'package:koushuu_system/widgets/pref_list_ui.dart';
import 'package:koushuu_system/widgets/top_bar.dart';

// 状態のデータクラス
class SearchPageStateData {
  DateTime? startDate;
  DateTime? endDate;
  String? freeKeyword;
  String? jimusho;
  String? category;

  SearchPageStateData({
    this.startDate,
    this.endDate,
    this.freeKeyword,
    this.jimusho,
    this.category,
  });

  SearchPageStateData copyWith({
    DateTime? startDate,
    DateTime? endDate,
    String? freeKeyword,
    String? jimusho,
    String? category,
  }) {
    return SearchPageStateData(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      freeKeyword: freeKeyword ?? this.freeKeyword,
      jimusho: jimusho ?? this.jimusho,
      category: category ?? this.category,
    );
  }
}

// 状態を保持するためのProviderを追加
final searchPageStateProvider =
    StateNotifierProvider<SearchPageStateNotifier, SearchPageStateData>(
        (ref) => SearchPageStateNotifier());

// 状態を管理するNotifier
class SearchPageStateNotifier extends StateNotifier<SearchPageStateData> {
  SearchPageStateNotifier()
      : super(SearchPageStateData(
          startDate: DateTime.now(),
          endDate: DateTime.now().add(const Duration(days: 365)),
          freeKeyword: '',
          jimusho: null,
          category: null,
        ));

  void updateStartDate(DateTime? date) {
    state = state.copyWith(startDate: date);
  }

  void updateEndDate(DateTime? date) {
    state = state.copyWith(endDate: date);
  }

  void updateFreeKeyword(String? keyword) {
    state = state.copyWith(freeKeyword: keyword);
  }

  void updateJimusho(String? jimusho) {
    state = state.copyWith(jimusho: jimusho);
  }

  void updateCategory(String? category) {
    state = state.copyWith(category: category);
  }

  void reset() {
    state = SearchPageStateData(
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 365)),
      freeKeyword: '',
      jimusho: null,
      category: null,
    );
  }
}

class SearchPage extends ConsumerStatefulWidget {
  const SearchPage({super.key, required this.title});

  final String title;

  @override
  ConsumerState<SearchPage> createState() => SearchPageState();
}

class SearchPageState extends ConsumerState<SearchPage> {
  String? freeKeyword = '';
  final formKey = GlobalKey<FormBuilderState>();
  final dteController = TextEditingController();
  final freeKeywordController = TextEditingController();

  DateTime? startDate = DateTime.now();
  DateTime? endDate = DateTime.now().add(const Duration(days: 365));

  // 日付フォーマット
  String getDateRangeText() {
    final DateFormat formatter = DateFormat('yyyy/MM/dd');
    String strFormatted = "";
    if (startDate != null && endDate != null) {
      strFormatted =
          '${formatter.format(startDate!)} ～ ${formatter.format(endDate!)}';
    }
    return strFormatted;
  }

  @override
  void initState() {
    super.initState();
    // Providerから状態を取得してコントローラーに設定
    final stateData = ref.read(searchPageStateProvider);

    startDate = stateData.startDate ?? DateTime.now();
    endDate =
        stateData.endDate ?? DateTime.now().add(const Duration(days: 365));
    dteController.text = getDateRangeText();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FormBuilder(
        key: formKey,
        child: Column(
          children: [
            Row(
              children: [
                TopBar(
                  title: widget.title,
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    // 事務所ドロップダウン
                    const FieldTitle(
                      title: "事務所",
                    ),
                    const PrefListUi(
                      "pref",
                    ),
                    const SizedBox(height: 16),

                    // 講習カテゴリドロップダウン
                    const FieldTitle(
                      title: "講習カテゴリ",
                    ),

                    const CategoryListUi(
                      "category",
                    ),
                    const SizedBox(height: 16),

                    // 講習名（フリーワード）
                    const FieldTitle(
                      title: "講習名（フリーワード）",
                    ),

                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: FormBuilderTextField(
                        controller: freeKeywordController,
                        name: 'keyword',
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 日付選択
                    const FieldTitle(
                      title: "日付",
                    ),

                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: FormBuilderTextField(
                        name: 'dte',
                        readOnly: true,
                        controller: dteController,
                        textAlign: TextAlign.center,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return '日付を選択してください。';
                          }
                          return null;
                        },
                      ),
                      //Center(child: Text(getDateRangeText())),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        DateTime? newDate = await showDatePicker(
                          context: context,
                          locale: const Locale("ja", "JA"),
                          initialDate: DateTime.now(),
                          firstDate: DateTime(DateTime.now().year - 1),
                          lastDate: DateTime(DateTime.now().year + 5),
                        );

                        if (newDate != null) {
                          ref
                              .read(searchPageStateProvider.notifier)
                              .updateStartDate(newDate);
                          ref
                              .read(searchPageStateProvider.notifier)
                              .updateEndDate(
                                  newDate.add(const Duration(days: 365)));

                          setState(() {
                            startDate = newDate;
                            endDate = newDate.add(const Duration(days: 365));
                            dteController.text = getDateRangeText();
                          });
                        }
                      },
                      child: const Text('日付を選択'),
                    ),
                    const SizedBox(height: 16),

                    // 検索ボタン
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.greenAccent.shade700,
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                      ),
                      onPressed: () {
                        // Form is valid
                        if (formKey.currentState?.validate() ?? false) {
                          final jimusho =
                              ref.read(prefValueProvider.notifier).state;
                          final category =
                              ref.read(categoryValueProvider.notifier).state;

                          context.go("/result", extra: {
                            "jimusho": jimusho!,
                            "category": category!,
                            "keyword": freeKeywordController.text,
                            "startDate": startDate.toString(),
                            "endDate": endDate.toString(),
                          });
                        }
                      },
                      child: const Text('検索',
                          style: TextStyle(fontSize: 18, color: Colors.white)),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const BottomBar(title: ''),
    );
  }
}
