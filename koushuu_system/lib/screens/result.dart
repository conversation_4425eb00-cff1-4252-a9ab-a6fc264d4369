import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:intl/intl.dart';
import 'package:koushuu_system/models/course.dart';
import 'package:koushuu_system/models/schedule_data.dart';
import 'package:koushuu_system/repositories/course_repository.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/footer_box.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/expandable_bar.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class SearchResultPage extends HookConsumerWidget {
  const SearchResultPage({
    super.key,
    required this.title,
    required this.jimusho,
    required this.category,
    this.keyword,
    required this.startDate,
    required this.endDate,
  });

  final String title;
  final String jimusho;
  final String category;
  final String? keyword;
  final DateTime startDate;
  final DateTime endDate;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final DateFormat formatter = DateFormat('yyyy/MM/dd');
    final String formattedStartDate = formatter.format(startDate);
    final String formattedEndDate = formatter.format(endDate);

    final ScrollController scrollController = useScrollController();
    final fetchAsync = ref.watch(fetchCourseProvider(
        jimusho, category, keyword, formattedStartDate, formattedEndDate));

    return Scaffold(
        body: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            children: <Widget>[
              Row(
                children: [
                  TopBar(
                    title: title,
                  ),
                ],
              ),
              // Body widgets
              // const Row(
              //   children: [
              //     Expanded(
              //       child: Steps(
              //         activeIndex: 2,
              //       ),
              //     ),
              //   ],
              // ),
              const Row(
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            height: 20,
                          ),
                          Text("ご希望のコースをタップして開き、左右にスワイプすると、全日程をご覧いただけます。"),
                          Text("受講要件が不明な方は、受講要件確認ボタンを押してください。"),
                          SizedBox(
                            height: 20,
                          ),
                          Divider(),
                          Text("◯       定員に空きがあります"),
                          //Text("1〜5   予約残数が５名以下の場合、実際に空いている数を表示します。"),
                          Text("✖️       予約を締切ました"),
                          // Text("●       中止"),
                          SizedBox(
                            height: 20,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),

              switch (fetchAsync) {
                AsyncData(:final value) => value.isNotEmpty
                    ? Column(
                        children: value
                            .map((it) => buildCourseBlockWidget(
                                  it["kosyu_code"],
                                  it["kosyu_number"],
                                  it["kosyu_omit"],
                                  it["kaijyo_data"],
                                  it["subtitle"],
                                ))
                            .toList())
                    : const DefaultText(
                        txt: "ご指定の条件に対して、講習が見つかりませんでした。",
                      ),
                AsyncError(:final error) => Text('error: $error'),
                _ => const Center(child: CircularProgressIndicator()),
              },
              const SizedBox(
                height: 20,
              ),
              const SizedBox(
                height: 20,
              ),
              const Row(
                children: [
                  FooterBox(),
                ],
              ),
            ],
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}

Widget buildCourseBlockWidget(
  String koshuuCode,
  String koshuuNumber,
  String titleBlock,
  List<dynamic> lstKaijyo,
  String? subtitle,
) {
  return Column(children: [
    Container(
      height: 50,
      color: Colors.grey,
      child: Column(
        children: [
          Row(
            children: [
              DefaultText(txt: titleBlock),
            ],
          ),
          Row(
            children: [
              DefaultText(
                txt: subtitle ?? "",
                fontSize: 14,
              ),
            ],
          ),
        ],
      ),
    ),
    ...lstKaijyo.map(
      (it) {
        final attendDaysListByMonth = it["attend_days_list_by_month"] as List?;
        // print(it.toString());
        // attend_days_list_by_month が null の場合に対応
        if (attendDaysListByMonth == null || attendDaysListByMonth.isEmpty) {
          return ExpandableBar(
            title: it["kaijyo_name"],
            titleDesc: titleBlock, //it["kaijyo_code"],
            scheduleData: const [],
            koushuuCode: koshuuCode,
            koushuuNumber: koshuuNumber,
            jimusyoCode: it["jimusyo_code"],
            jimusyoName: it["jimusyo_name"],
            kaijo: it["kaijyo_name"],
          );
        }

        final scheduleData = attendDaysListByMonth.map((e) {
          final attendDays = (e["attend_days"] as List).map((day) {
            return AttendDay(
              start_date: day.start_date,
              end_date: day.end_date,
              request_count: day.request_count,
              capacity_count: day.capacity_count,
              suspended_flag: day.suspended_flag,
              course_code: day.course_code,
              course_name: day.course_name,
              course_days: day.course_days,
              price: day.price,
            );
          }).toList();

          return ScheduleData(
            month: e["month"],
            attend_days: attendDays,
          );
        }).toList();

        return ExpandableBar(
          title: it["kaijyo_name"],
          titleDesc: titleBlock, // it["kaijyo_code"],
          scheduleData: scheduleData,
          koushuuCode: koshuuCode,
          koushuuNumber: koshuuNumber,
          jimusyoCode: it["jimusyo_code"],
          jimusyoName: it["jimusyo_name"],
          kaijo: it["kaijyo_name"],
        );
      },
    ),
  ]);
}
