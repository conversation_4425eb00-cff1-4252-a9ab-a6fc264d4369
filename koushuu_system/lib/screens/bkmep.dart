import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/core/encryptor.dart';
import 'package:koushuu_system/core/utils.dart';
import 'package:koushuu_system/models/participant.dart';
import 'package:koushuu_system/providers/appstate_provider.dart';
import 'package:koushuu_system/repositories/user_repository.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/error_info.dart';
import 'package:koushuu_system/widgets/footer_box.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/core/constants.dart' as constants;

class MailEndPointPage extends ConsumerStatefulWidget {
  const MailEndPointPage(
      {super.key, this.title = "", required this.ek, required this.uid});

  final String title;
  final String ek;
  final String uid;

  @override
  ConsumerState<MailEndPointPage> createState() => _MailEndPointPageState();
}

class _MailEndPointPageState extends ConsumerState<MailEndPointPage> {
  final ScrollController _scrollController = ScrollController();
  @override
  void initState() {
    SessionManager().set('uketsuke_id', widget.uid);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    //if no parameter then go to home
    if (widget.ek == "" && widget.uid == "") {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        GoRouter.of(context).go('/');
      });
    }

    Map<String, dynamic> kData = {};
    if (widget.ek != "") {
      try {
        final strDec = Encryptor.decryptData(widget.ek);
        kData.addAll(Utils.extractData(strDec));
      } catch (e) {
        GoRouter.of(context).go('/');
      }

      if (kData.isEmpty) {
        GoRouter.of(context).go('/');
      }

      //TODO remove this after testing
      print(kData.toString());

      //session step info update
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(appStateProvider.notifier).updateStep(kData["c"]);
      });

      if (kData["c"] == constants.STEP1) {
        final applicantId = kData["applicant_id"] ?? "";
        final applicantName = kData["applicant_name"] ?? "";
        final email = kData["email"] ?? "";

        if (email == "") {
          return const ErrorInfo(txt: "パラメーターが正しくありません。");
        }

        //check user exits with email
        final userExistsAsync = ref.watch(checkUserProvider(email));

        return Scaffold(
          body: userExistsAsync.when(
            data: (data) {
              if (data == "1") {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  //申込ID
                  ref
                      .read(appStateProvider.notifier)
                      .updateApplicationId(applicantId);
                  //受付ID
                  ref
                      .read(appStateProvider.notifier)
                      .updateUketsukeId(widget.uid);
                  GoRouter.of(context).go('/login');
                });
              } else {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  GoRouter.of(context).go('/register', extra: {
                    "mid": applicantId,
                    "mname": applicantName,
                    "email": email,
                  });
                });
              }
              return Container();
            },
            error: (e, _) => Center(child: DefaultText(txt: e.toString())),
            loading: () => const Center(child: CircularProgressIndicator()),
          ),
        );
      } else if (kData["c"] == constants.STEP2) {
        final participantId = kData["participant_id"] ?? "";
        final uketsukeId = kData["uketsuke_id"] ?? "";
        final applicationId = kData["application_id"] ?? "";
        final applicantName = kData["applicant_name"] ?? "";
        final email = kData["email"] ?? "";

        if (participantId == "" && uketsukeId == "") {
          return const ErrorInfo(txt: "パラメーターが正しくありません。");
        }

        //Get pariticipant info
        // final participantExistsAsync =
        //     ref.watch(getParticipantProvider(participantId, uketsukeId));

        //check user exits with email
        final userExistsAsync = ref.watch(checkUserProvider(email));

        return Scaffold(
          body: userExistsAsync.when(
            data: (data) {
              if (data == "1") {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  //申込ID
                  ref
                      .read(appStateProvider.notifier)
                      .updateApplicationId(applicationId);
                  //受付ID
                  ref
                      .read(appStateProvider.notifier)
                      .updateUketsukeId(uketsukeId);
                  GoRouter.of(context).go('/login');
                });
              } else {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  GoRouter.of(context).go('/register', extra: {
                    "mid": participantId,
                    "mname": applicantName,
                    "email": email,
                  });
                });
              }
              return Container();
            },
            error: (e, _) => Center(child: DefaultText(txt: e.toString())),
            loading: () => const Center(child: CircularProgressIndicator()),
          ),
        );
      } else if (kData["c"] == constants.STEP5) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          GoRouter.of(context).go('/changereqend?uid=${widget.uid}');
        });
      }
    } else {
      if (widget.ek == "" && widget.uid != "") {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Participant p = const Participant();
          //TODO get particiaptn info from seminas and set to external request flg to participant register page
          GoRouter.of(context).go("/rparticipant", extra: p);
        });
      }
    }

    return Scaffold(
        body: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Row(
                children: [
                  TopBar(
                    title: widget.title,
                  ),
                ],
              ),

              const SizedBox(height: 30),

              // Requirements Section
              const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  DefaultText(
                    txt: "パラメータが正しくありません。",
                  ),
                ],
              ),

              const SizedBox(height: 30),
              // Buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CoreButton(
                    onPressed: () {
                      GoRouter.of(context).go('/');
                    },
                    title: 'TOPへ',
                  ),
                ],
              ),
              const SizedBox(height: 30),
              const Row(
                children: [
                  FooterBox(),
                ],
              ),
            ],
          ),
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }

  // Widget _blankWidget(BuildContext context) {
  //   return Scaffold(
  //       body: SingleChildScrollView(
  //         controller: _scrollController,
  //         child: Column(
  //           mainAxisAlignment: MainAxisAlignment.center,
  //           crossAxisAlignment: CrossAxisAlignment.center,
  //           children: <Widget>[
  //             Row(
  //               children: [
  //                 TopBar(
  //                   title: widget.title,
  //                 ),
  //               ],
  //             ),

  //             const SizedBox(height: 30),

  //             // Requirements Section
  //             const Row(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               crossAxisAlignment: CrossAxisAlignment.center,
  //               children: [
  //                 DefaultText(
  //                   txt: "パラメータが正しくありません。",
  //                 ),
  //               ],
  //             ),

  //             const SizedBox(height: 30),
  //             // Buttons
  //             Row(
  //               mainAxisAlignment: MainAxisAlignment.center,
  //               children: [
  //                 CoreButton(
  //                   onPressed: () {
  //                     GoRouter.of(context).go('/');
  //                   },
  //                   title: 'TOPへ',
  //                 ),
  //               ],
  //             ),
  //             const SizedBox(height: 30),
  //             const Row(
  //               children: [
  //                 FooterBox(),
  //               ],
  //             ),
  //           ],
  //         ),
  //       ),
  //       bottomNavigationBar: const BottomBar(title: ''));
  // }
}
