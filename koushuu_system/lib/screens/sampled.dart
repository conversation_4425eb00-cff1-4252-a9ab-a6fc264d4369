import 'package:flutter/material.dart';

class SampedPage extends StatefulWidget {
  const SampedPage({super.key, required this.title});

  final String title;

  @override
  State<SampedPage> createState() => SampedPageState();
}

class SampedPageState extends State<SampedPage> {
  // Define text controllers for each input field
  final TextEditingController reservationCountController =
      TextEditingController(text: '3');
  final TextEditingController applicationIdController =
      TextEditingController(text: '11125656');
  final TextEditingController applicantNameController =
      TextEditingController(text: '株式会社 サイン');
  final TextEditingController emailController =
      TextEditingController(text: '<EMAIL>');
  final TextEditingController reEmailController =
      TextEditingController(text: '<EMAIL>');

  @override
  void dispose() {
    // Dispose of the controllers when the widget is disposed
    reservationCountController.dispose();
    applicationIdController.dispose();
    applicantNameController.dispose();
    emailController.dispose();
    reEmailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Top Section (会場, 日程, コース, 料金)
            Container(
              padding: EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('会場: 埼玉センタ'),
                  Text('日程: 11月2日(土)〜4日(月)'),
                  Text('コース: 16H'),
                  Text('料金: 49,000円'),
                ],
              ),
            ),
            SizedBox(height: 10),

            // Reservation Section
            Row(
              children: [
                Text('予約人数'),
                SizedBox(width: 10),
                Expanded(
                  child: TextField(
                    controller: reservationCountController,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: '3',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),

            // Requirements Section
            Container(
              padding: EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black),
                borderRadius: BorderRadius.circular(8.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '受講要件',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text('・クレーン、デリック運転免許を有する'),
                  Text('・移動式クレーン運転士免許を有する'),
                  Text('・床上操作式クレーン運転技能講習を修了している'),
                  Text('・玉掛け技能講習を修了している'),
                  SizedBox(height: 5),
                  Text(
                    '必要書類',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text('受講申込書に記載の必要書類 A・B'),
                ],
              ),
            ),
            SizedBox(height: 10),

            // Application Details Section
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: applicationIdController,
                  decoration: InputDecoration(
                    labelText: '申込書ID',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 10),
                TextField(
                  controller: applicantNameController,
                  decoration: InputDecoration(
                    labelText: '申込者名',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 10),
                TextField(
                  controller: emailController,
                  decoration: InputDecoration(
                    labelText: 'メールアドレス',
                    border: OutlineInputBorder(),
                  ),
                ),
                SizedBox(height: 10),
                TextField(
                  controller: reEmailController,
                  decoration: InputDecoration(
                    labelText: '再メールアドレス',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),

            // Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  onPressed: () {
                    // Handle cancel button action
                    setState(() {
                      reservationCountController.text = '';
                      applicationIdController.text = '';
                      applicantNameController.text = '';
                      emailController.text = '';
                      reEmailController.text = '';
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                  ),
                  child: Text('キャンセル'),
                ),
                ElevatedButton(
                  onPressed: () {
                    // Handle next button action
                    // Add validation or next step here
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                  child: Text('次に進む'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
