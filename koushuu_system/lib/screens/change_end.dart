import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:koushuu_system/models/rerequest.dart';
import 'package:koushuu_system/repositories/address_repository.dart';
import 'package:koushuu_system/repositories/jimusho_repository.dart';
import 'package:koushuu_system/repositories/request_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/error_info.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:koushuu_system/core/constants.dart' as constants;
import 'package:loader_overlay/loader_overlay.dart';

final jimushoValProvider = StateProvider<String?>((ref) => null);

class ChangeEndPage extends HookConsumerWidget {
  ChangeEndPage({super.key, required this.title, required this.uketsukeid});

  final String title;
  final String uketsukeid;
  final ScrollController _scrollController = ScrollController();

  ReRequestDoc tmp = const ReRequestDoc(
    uketsuke_id: "",
    name: "",
    name_kana: "",
    birth_day: "",
    zip_code: "",
    addr1: "",
    addr2: "",
    tel_no: "",
    mail_addr: "",
    soufu_name: "",
    soufu_zip_code: "",
    soufu_addr1: "",
    soufu_addr2: "",
    image_photo: "",
    filename_photo: "",
    filetype_photo: "",
    image_kakunin_omote: "",
    filename_kakunin_omote: "",
    filetype_kakunin_omote: "",
    image_kakunin_ura: "",
    filename_kakunin_ura: "",
    filetype_kakunin_ura: "",
    image_kakunin_atsumi: "",
    filename_kakunin_atsumi: "",
    filetype_kakunin_atsumi: "",
    image_kakunin_rename: "",
    filename_kakunin_rename: "",
    filetype_kakunin_rename: "",
    url: "",
  );

  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // final fetchSaihakkoIraiReadAsync =
    //     ref.read(readReRequestProvider(uketsukeid));
    final fetchSaihakkoUketsukeReadAsync =
        ref.watch(readReRequestUketsukeProvider(uketsukeid));

    //jimusho
    List<Map<String, dynamic>> lstJimusho = [];
    final fetchJimushoAsync = ref.watch(fetchJimushoListProvider);
    final selectJimushoValue = ref.watch(jimushoValProvider);

    return Scaffold(
        body: FormBuilder(
            key: _formKey,
            child: SingleChildScrollView(
              controller: _scrollController,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: <Widget>[
                    const Row(
                      children: [
                        TopBar(
                          title: '修了者情報・連絡先の入力',
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    fetchSaihakkoUketsukeReadAsync.when(
                      data: (dt) {
                        if (dt["responsedata"]["irai_data"] != null) {
                          tmp = ReRequestDoc.fromJson(dt["responsedata"]);
                        } else {
                          return const ErrorInfo(txt: "データ読み込みできませんでした！");
                        }

                        var selectedReason = '1';
                        if (tmp.irai_data!.lost_flag == "1") {
                          selectedReason = '1';
                        }
                        if (tmp.irai_data!.change_name_flag == "1") {
                          selectedReason = '2';
                        }
                        if (tmp.irai_data!.damage_flag == "1") {
                          selectedReason = '3';
                        }
                        if (tmp.irai_data!.other_flag == "1") {
                          selectedReason = '4';
                        }

                        return Row(
                          children: [
                            // Form Section
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(20.0),
                                child: Column(
                                  children: [
                                    _buildTextField(
                                      "name",
                                      "氏名",
                                      false,
                                      FormBuilderValidators.compose([
                                        FormBuilderValidators.required(),
                                      ]),
                                      true,
                                      initialValue: tmp.name ??
                                          ((tmp.irai_data!.name_sei ?? "") +
                                              (tmp.irai_data!.name_mei ?? "")),
                                    ),
                                    _buildTextField(
                                      'kyuusei',
                                      '旧姓',
                                      false,
                                      null,
                                      false,
                                      initialValue: tmp.irai_data!.old_name,
                                    ),
                                    _buildTextField(
                                      'furigana',
                                      'ふりがな',
                                      false,
                                      null,
                                      false,
                                      initialValue:
                                          tmp.irai_data!.name_name_kana,
                                    ),
                                    _buildTextField(
                                      'uketsukeid',
                                      '受付ID',
                                      false,
                                      null,
                                      true,
                                      initialValue: tmp.uketsuke_id ??
                                          tmp.irai_data!.uketsuke_id,
                                      enabled: false,
                                    ),
                                    _buildTextField(
                                      'birthday',
                                      '生年月日',
                                      false,
                                      null,
                                      true,
                                      initialValue: tmp.birth_day ??
                                          tmp.irai_data!.birth_day,
                                      enabled: false,
                                    ),
                                    _buildTextField(
                                      'kosyu_type',
                                      '申請する修了証',
                                      false,
                                      FormBuilderValidators.compose([
                                        FormBuilderValidators.required(),
                                      ]),
                                      true,
                                      initialValue: tmp.irai_data!.kosyu_type,
                                      enabled: false,
                                    ),
                                    fetchJimushoAsync.when(
                                      data: (val) {
                                        // lstJimusho = val;
                                        lstJimusho.clear();

                                        return FormBuilderDropdown<String>(
                                          name: "jimusyo_code",
                                          enabled: false,
                                          initialValue:
                                              tmp.irai_data!.jimusyo_code,
                                          onChanged: (newValue) {
                                            ref
                                                .read(
                                                    jimushoValProvider.notifier)
                                                .state = newValue!;
                                          },
                                          items: val.map((loc) {
                                            lstJimusho.add(loc);
                                            return DropdownMenuItem<String>(
                                              value: loc["jimusyo_code"],
                                              child: Text(loc["jimusyo_name"]),
                                            );
                                          }).toList(),
                                          isExpanded: true,
                                          validator:
                                              FormBuilderValidators.compose([
                                            FormBuilderValidators.required(),
                                          ]),
                                          decoration: InputDecoration(
                                            filled: true,
                                            fillColor:
                                                Colors.orangeAccent.shade100,
                                            border: const OutlineInputBorder(),
                                            labelText: "取得した事務所",
                                          ),
                                        );
                                      },
                                      loading: () => const Center(
                                          child: CircularProgressIndicator()),
                                      error: (e, _) =>
                                          ErrorInfo(txt: e.toString()),
                                    ),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: FormBuilderRadioGroup<String>(
                                            wrapAlignment: WrapAlignment.start,
                                            name: 'reason',
                                            enabled: false,
                                            initialValue: selectedReason,
                                            options: const [
                                              FormBuilderFieldOption(
                                                  value: '1',
                                                  child: Text('紛失')),
                                              FormBuilderFieldOption(
                                                  value: '2',
                                                  child: Text('氏名変更')),
                                              FormBuilderFieldOption(
                                                  value: '3',
                                                  child: Text('破損・汚れ')),
                                              FormBuilderFieldOption(
                                                  value: '4',
                                                  child: Text('その他')),
                                            ],
                                            validator: (value) {
                                              if (value == null ||
                                                  value.isEmpty) {
                                                return '選択してください。';
                                              }
                                              return null;
                                            }, // 初期選択
                                          ),
                                        ),
                                      ],
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 5.0),
                                      child: FormBuilderTextField(
                                        enabled: false,
                                        minLines: 5,
                                        maxLines: 20,
                                        initialValue: tmp.irai_data!.comment,
                                        // controller: otherController,
                                        decoration: const InputDecoration(
                                          labelText: "その他の理由",
                                          border: OutlineInputBorder(),
                                        ),
                                        name: 'other',
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),
                                    _buildTextField(
                                        'tel', '電話番号', false, null, true,
                                        initialValue: tmp.irai_data!.tel_no,
                                        enabled: false),
                                    _buildTextField(
                                        'email', 'メールアドレス', false, null, true,
                                        initialValue: tmp.irai_data!.mail_addr,
                                        enabled: false),
                                    _buildTextField('comment', '協会からの連絡事項',
                                        false, null, true,
                                        initialValue: tmp.irai_data!.comment,
                                        enabled: false),
                                    const SizedBox(
                                      height: 30,
                                    ),
                                    const Divider(),
                                    _buildPhotoField(
                                        "photo1", '本人写真', tmp.image_photo ?? "",
                                        filename: tmp.filename_photo ?? "",
                                        filetype: tmp.filetype_photo ?? ""),
                                    _buildPhotoField("photo2", '本人確認書類　表',
                                        tmp.image_kakunin_omote ?? "",
                                        filename:
                                            tmp.filename_kakunin_omote ?? "",
                                        filetype:
                                            tmp.filetype_kakunin_omote ?? ""),
                                    _buildPhotoField("photo3", '本人確認書類　裏',
                                        tmp.image_kakunin_ura ?? "",
                                        filename:
                                            tmp.filename_kakunin_ura ?? "",
                                        filetype:
                                            tmp.filetype_kakunin_ura ?? ""),
                                    _buildPhotoField("photo4", '本人確認書類　厚み',
                                        tmp.image_kakunin_atsumi ?? "",
                                        filename:
                                            tmp.filename_kakunin_atsumi ?? "",
                                        filetype:
                                            tmp.filetype_kakunin_atsumi ?? ""),
                                    _buildPhotoField("photo5", '旧姓通称確認書類',
                                        tmp.image_kakunin_rename ?? "",
                                        filename:
                                            tmp.filename_kakunin_rename ?? "",
                                        filetype:
                                            tmp.filetype_kakunin_rename ?? ""),
                                    Center(
                                        child: Image.asset(
                                      'assets/iddesc.png',
                                      height: 300,
                                    )),
                                    const Divider(),
                                    _buildTextField(
                                      'soufuname',
                                      '送付先名',
                                      false,
                                      FormBuilderValidators.compose(
                                        [FormBuilderValidators.required()],
                                      ),
                                      true,
                                      initialValue: tmp.soufu_name,
                                    ),
                                    FormBuilderTextField(
                                      name: "postal",
                                      enabled: true,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp(r'[-]|[0-9]')),
                                        LengthLimitingTextInputFormatter(8),
                                      ],
                                      validator: FormBuilderValidators.compose(
                                        [
                                          FormBuilderValidators.required(),
                                          FormBuilderValidators.match(
                                              RegExp(r'[-]|[0-9]')),
                                        ],
                                      ),
                                      initialValue: tmp.zip_code,
                                      decoration: InputDecoration(
                                        filled: true,
                                        fillColor: Colors.orangeAccent.shade100,
                                        labelText: "〒",
                                        border: const OutlineInputBorder(),
                                      ),
                                      onSubmitted: (value) async {
                                        if (value!.length >= 7) {
                                          context.loaderOverlay.show();
                                          final res = await ref
                                              .read(addressRepositoryProvider)
                                              .fetchData(value);

                                          context.loaderOverlay.hide();

                                          if (res.city != "") {
                                            _formKey
                                                .currentState?.fields['addr1']
                                                ?.didChange(res.pref);
                                            _formKey
                                                .currentState?.fields['addr2']
                                                ?.didChange(
                                                    res.city + res.area);
                                          } else {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              const SnackBar(
                                                  content:
                                                      Text('住所見つかりませんでした。')),
                                            );
                                            _formKey
                                                .currentState?.fields['addr1']
                                                ?.didChange("");
                                            _formKey
                                                .currentState?.fields['addr2']
                                                ?.didChange("");
                                          }
                                        }
                                      },
                                    ),
                                    _buildTextField(
                                        'addr1', '住所1', false, null, false,
                                        initialValue: tmp.addr1),
                                    _buildTextField(
                                        'addr2', '住所2', false, null, false,
                                        initialValue: tmp.addr2),
                                    const SizedBox(
                                      height: 30,
                                    ),
                                    Center(
                                      child: CoreButton(
                                        onPressed: () async {
                                          // Form is valid
                                          if (_formKey.currentState
                                                  ?.validate() ??
                                              false) {
                                            context.loaderOverlay.show();

                                            //Photo1
                                            final photo1Images = _formKey
                                                .currentState!
                                                .fields['photo1']
                                                ?.value;
                                            final photo1Data =
                                                await doPrepareImageData(
                                                    photo1Images,
                                                    filename:
                                                        tmp.filename_photo,
                                                    filetype:
                                                        tmp.filetype_photo);
                                            final base64photo1 =
                                                photo1Data["fdata"];
                                            final base64photo1Name =
                                                photo1Data["fname"];
                                            final base64photo1Type =
                                                photo1Data["ftype"];

                                            //Photo2
                                            final photo2Images = _formKey
                                                .currentState!
                                                .fields['photo2']
                                                ?.value;
                                            final photo2Data =
                                                await doPrepareImageData(
                                                    photo2Images,
                                                    filename: tmp
                                                        .filename_kakunin_omote,
                                                    filetype: tmp
                                                        .filetype_kakunin_omote);
                                            final base64photo2 =
                                                photo2Data["fdata"];
                                            final base64photo2Name =
                                                photo2Data["fname"];
                                            final base64photo2Type =
                                                photo2Data["ftype"];

                                            //Photo3
                                            final photo3Images = _formKey
                                                .currentState!
                                                .fields['photo3']
                                                ?.value;
                                            final photo3Data =
                                                await doPrepareImageData(
                                                    photo3Images,
                                                    filename: tmp
                                                        .filename_kakunin_ura,
                                                    filetype: tmp
                                                        .filetype_kakunin_ura);
                                            final base64photo3 =
                                                photo3Data["fdata"];
                                            final base64photo3Name =
                                                photo3Data["fname"];
                                            final base64photo3Type =
                                                photo3Data["ftype"];
                                            //Photo4
                                            final photo4Images = _formKey
                                                .currentState!
                                                .fields['photo4']
                                                ?.value;
                                            final photo4Data =
                                                await doPrepareImageData(
                                                    photo4Images,
                                                    filename: tmp
                                                        .filename_kakunin_atsumi,
                                                    filetype: tmp
                                                        .filetype_kakunin_atsumi);
                                            final base64photo4 =
                                                photo4Data["fdata"];
                                            final base64photo4Name =
                                                photo4Data["fname"];
                                            final base64photo4Type =
                                                photo4Data["ftype"];
                                            //Photo5
                                            final photo5Images = _formKey
                                                .currentState!
                                                .fields['photo5']
                                                ?.value;
                                            final photo5Data =
                                                await doPrepareImageData(
                                                    photo5Images,
                                                    filename: tmp
                                                        .filename_kakunin_rename,
                                                    filetype: tmp
                                                        .filetype_kakunin_rename);
                                            final base65photo5 =
                                                photo5Data["fdata"];
                                            final base65photo5Name =
                                                photo5Data["fname"];
                                            final base65photo5Type =
                                                photo5Data["ftype"];

                                            ReRequestDoc tmpR = ReRequestDoc(
                                                uketsuke_id: _formKey
                                                    .currentState!
                                                    .fields['uketsukeid']
                                                    ?.value,
                                                name: _formKey.currentState!
                                                    .fields['name']?.value,
                                                name_kana: _formKey
                                                    .currentState!
                                                    .fields['furigana']
                                                    ?.value,
                                                birth_day: _formKey
                                                    .currentState!
                                                    .fields['birthday']
                                                    ?.value,
                                                zip_code: _formKey.currentState!
                                                    .fields['postal']?.value,
                                                addr1: _formKey.currentState!
                                                    .fields['addr1']?.value,
                                                addr2: _formKey.currentState!
                                                    .fields['addr2']?.value,
                                                tel_no: _formKey.currentState!
                                                    .fields['tel']?.value,
                                                mail_addr: _formKey
                                                    .currentState!
                                                    .fields['email']
                                                    ?.value,
                                                soufu_name: _formKey
                                                    .currentState!
                                                    .fields['soufuname']
                                                    ?.value,
                                                soufu_zip_code: _formKey
                                                    .currentState!
                                                    .fields['postal']
                                                    ?.value,
                                                soufu_addr1: _formKey
                                                    .currentState!
                                                    .fields['addr1']
                                                    ?.value,
                                                soufu_addr2: _formKey
                                                    .currentState!
                                                    .fields['addr2']
                                                    ?.value,
                                                image_photo: base64photo1,
                                                filename_photo:
                                                    base64photo1Name,
                                                filetype_photo: base64photo1Type,
                                                image_kakunin_omote: base64photo2,
                                                filename_kakunin_omote: base64photo2Name,
                                                filetype_kakunin_omote: base64photo2Type,
                                                image_kakunin_ura: base64photo3,
                                                filename_kakunin_ura: base64photo3Name,
                                                filetype_kakunin_ura: base64photo3Type,
                                                image_kakunin_atsumi: base64photo4,
                                                filename_kakunin_atsumi: base64photo4Name,
                                                filetype_kakunin_atsumi: base64photo4Type,
                                                image_kakunin_rename: base65photo5,
                                                filename_kakunin_rename: base65photo5Name,
                                                filetype_kakunin_rename: base65photo5Type,
                                                url: tmp.irai_data?.url ?? "");

                                            final res = await ref
                                                .read(requestRepositoryProvider)
                                                .makeReRequestUketsuke(tmpR);

                                            if (res['status'] !=
                                                constants.API_RES_SUCCESS) {
                                              ScaffoldMessenger.of(context)
                                                  .showSnackBar(
                                                const SnackBar(
                                                    content: Text(
                                                        '再発行リクエスト作成にエラーが発生しました！')),
                                              );
                                            } else {
                                              GoRouter.of(context)
                                                  .go('/changeend');
                                            }
                                          }
                                        },
                                        title: '受付の終了',
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      error: (e, _) => const ErrorInfo(
                          txt: "データ読み込みできませんでした！"), //e.toString()),),],
                    ),
                  ]),
            )),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}

Widget _buildTextField(
  String name,
  String label,
  bool isSecure,
  String? Function(String?)? validator,
  bool? isRequired, {
  bool? enabled,
  List<TextInputFormatter>? inputFormatter,
  String? initialValue,
}) {
  isRequired ??= false;
  enabled ??= true;
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 5.0),
    child: FormBuilderTextField(
      name: name,
      initialValue: initialValue,
      enabled: enabled,
      obscureText: isSecure,
      inputFormatters: inputFormatter == [] ? null : inputFormatter,
      validator: validator,
      decoration: InputDecoration(
        filled: isRequired,
        fillColor:
            isRequired ? Colors.orangeAccent.shade100 : Colors.transparent,
        labelText: label,
        border: const OutlineInputBorder(),
      ),
    ),
  );
}

// Widget _buildTextField(
//     String name,
//     String label,
//     TextEditingController controller,
//     bool isSecure,
//     String? Function(String?)? validator,
//     bool? isRequired) {
//   isRequired ??= false;
//   return Padding(
//     padding: const EdgeInsets.symmetric(vertical: 5.0),
//     child: FormBuilderTextField(
//       name: name,
//       obscureText: isSecure,
//       controller: controller,
//       validator: isRequired
//           ? (value) {
//               if (value == null || value.isEmpty) {
//                 return '$labelを入力してください。';
//               }
//               return null;
//             }
//           : validator,
//       decoration: InputDecoration(
//         filled: isRequired,
//         fillColor:
//             isRequired ? Colors.orangeAccent.shade100 : Colors.transparent,
//         labelText: label,
//         border: const OutlineInputBorder(),
//       ),
//     ),
//   );
// }

// Widget _buildPhotoField(
//   String fieldName,
//   String lbl,
// ) {
//   return Padding(
//     padding: const EdgeInsets.all(8.0),
//     child: FormBuilderImagePicker(
//       name: fieldName,
//       maxImages: 1,
//       previewAutoSizeWidth: true,
//       decoration: InputDecoration(labelText: lbl),
//       //previewAutoSizeWidth: true,
//       //fit: BoxFit.cover,
//       backgroundColor: Colors.black54,
//       iconColor: Colors.white,
//       icon: Icons.camera_alt_outlined,
//     ),
//   );
// }

/// Base64 画像データを一時ファイルとして保存
// Future<File> convertBase64ToFile(
//     String filename, String filetype, String base64data) async {
//   Uint8List bytes = base64Decode(base64data);

//   io.Directory tempDir = io.Directory.systemTemp;
//   print(tempDir.path);
//   File file = File('${tempDir.path}/$filename');
//   return await file.writeAsBytes(bytes);
// }

Widget _buildPhotoField(String fieldName, String lbl, String base64data,
    {String filename = "", String filetype = ""}) {
  return Padding(
    padding: const EdgeInsets.all(8.0),
    child: FormBuilderImagePicker(
      name: fieldName,
      maxImages: 1,
      previewAutoSizeWidth: true,
      decoration: InputDecoration(labelText: lbl),
      backgroundColor: Colors.black54,
      iconColor: Colors.white,
      icon: Icons.camera_alt_outlined,
      initialValue: base64data != "" ? [base64Decode(base64data)] : [null],
      validator: FormBuilderValidators.compose([
        FormBuilderValidators.required(),
      ]),
    ),
  );
}

Future<Map<String, dynamic>> doPrepareImageData(selectedImages,
    {String? filename, String? filetype}) async {
  String fname = filename ?? "";
  String ftype = filetype ?? "";
  String fdata = "";

  if (selectedImages != null && selectedImages.isNotEmpty) {
    // Get selected image
    final imageFile = selectedImages[0];

    // print(imageFile);
    if (imageFile != null) {
      if (imageFile.runtimeType != Uint8List) {
        fname = imageFile.name;
        ftype = imageFile.mimeType!;

        // 画像をBase64にエンコード
        await imageFile.readAsBytes().then((bytes) {
          fdata = base64Encode(bytes);
          // debugPrint("Base64 Image: $f64data");
        }).catchError((e) {
          debugPrint("Error reading image file: $e");
        });
      } else {
        fdata = base64Encode(imageFile);
      }
    }
  }
  return {
    "fname": fname,
    "ftype": ftype,
    "fdata": fdata,
  };
}
