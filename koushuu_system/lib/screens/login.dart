import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_session_manager/flutter_session_manager.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:koushuu_system/providers/appstate_provider.dart';
import 'package:koushuu_system/repositories/user_repository.dart';
import 'package:koushuu_system/widgets/bottom_bar.dart';
import 'package:koushuu_system/widgets/core_button.dart';
import 'package:koushuu_system/widgets/default_text.dart';
import 'package:koushuu_system/widgets/top_bar.dart';
import 'package:koushuu_system/widgets/up_fab.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:koushuu_system/core/constants.dart' as constants;

final optValueProvider = StateProvider<String>((ref) {
  return "1";
});

class LoginPage extends ConsumerWidget {
  LoginPage({super.key, required this.title});

  final String title;

  final ScrollController _scrollController = ScrollController();
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final step = ref.read(appStateProvider).step;

    return Scaffold(
        body: FormBuilder(
          key: _formKey,
          child: SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const Row(
                  children: [
                    TopBar(
                      title: '受講者の登録',
                    ),
                  ],
                ),
                const SizedBox(
                  height: 30,
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20.0, right: 20.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            // Form Section
                            _buildTextField(
                              "email",
                              'メールアドレス',
                              false,
                              FormBuilderValidators.compose([
                                FormBuilderValidators.required(),
                                FormBuilderValidators.email(),
                              ]),
                            ),
                            _buildTextField(
                              "password",
                              'パスワード',
                              true,
                              FormBuilderValidators.compose([
                                FormBuilderValidators.required(),
                                // FormBuilderValidators.password(),
                              ]),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 20,
                ),
                step == constants.STEP2
                    ? Container()
                    : const Padding(
                        padding: EdgeInsets.only(left: 20.0, right: 20.0),
                        child: Row(children: [
                          DefaultText(txt: "申込者と受講者が一緒ですか？"),
                        ]),
                      ),
                step == constants.STEP2
                    ? Container()
                    : Padding(
                        padding: const EdgeInsets.all(20.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: FormBuilderRadioGroup<String>(
                                wrapAlignment: WrapAlignment.start,
                                name: 'reason',
                                initialValue: "1",
                                options: const [
                                  FormBuilderFieldOption(
                                      value: '1', child: Text('はい')),
                                  FormBuilderFieldOption(
                                      value: '2', child: Text('いいえ')),
                                ],
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return '選択してください。';
                                  }
                                  return null;
                                }, // 初期選択
                                onChanged: (val) {
                                  ref.read(optValueProvider.notifier).state =
                                      val!;
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                const SizedBox(
                  height: 20,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CoreButton(
                      onPressed: () {
                        GoRouter.of(context).go('/');
                      },
                      bgColor: Colors.red,
                      fgColor: Colors.white,
                      title: 'キャンセル',
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    CoreButton(
                      onPressed: () async {
                        //if (_formKey.currentState?.validate() ?? false) {
                        if (_formKey.currentState!.saveAndValidate()) {
                          Map<String, dynamic> formValues =
                              _formKey.currentState!.value;
                          String email = formValues["email"];
                          String password = formValues["password"];

                          context.loaderOverlay.show();
                          try {
                            final res = await ref
                                .read(userRepositoryProvider)
                                .login(email, password);

                            context.loaderOverlay.hide();

                            if (res.data["status"] ==
                                constants.API_RES_SUCCESS) {
                              // //申込ID
                              // ref
                              //     .read(appStateProvider.notifier)
                              //     .updateApplicationId(res.data["data"]
                              //             ["application_id"]
                              //         .toString());
                              // //ログインユーザー名
                              // ref
                              //     .read(appStateProvider.notifier)
                              //     .updateLoginName(
                              //         res.data["data"]["name"].toString());

                              //save info to session
                              await SessionManager().set(
                                  'application_id',
                                  res.data["data"]["application_id"]
                                      .toString());
                              await SessionManager()
                                  .set('login_name', res.data["data"]["name"]);
                              await SessionManager().set('login_email', email);
                              await SessionManager().set(
                                  'member_id', res.data["data"]["member_id"]);
                              await SessionManager()
                                  .set('isList', res.data["data"]["list"]);

                              if (step == constants.STEP2) {
                                GoRouter.of(context).go('/rparticipant');
                              } else if (ref
                                      .read(optValueProvider.notifier)
                                      .state ==
                                  "1") {
                                if (res.data["data"]["member_id"] != "0") {
                                  GoRouter.of(context).go('/rparticipant');
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text("受講者情報見つかりませんでした。"),
                                    ),
                                  );
                                }
                              } else {
                                if (res.data["data"]["list"] == "1") {
                                  GoRouter.of(context).go('/participants');
                                } else {
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    const SnackBar(
                                      content: Text("該当する申込データありません！"),
                                    ),
                                  );
                                }
                              }
                            } else {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(res.data["message"]),
                                ),
                              );
                            }
                          } catch (e) {
                            context.loaderOverlay.hide();
                            print(e);
                          }
                        }
                      },
                      bgColor: Colors.red,
                      fgColor: Colors.white,
                      title: 'ログイン',
                    ),
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
              ],
            ),
          ),
        ),
        floatingActionButton: UpFabButton(
          scrollViewController: _scrollController,
        ),
        bottomNavigationBar: const BottomBar(title: ''));
  }
}

Widget _buildTextField(String name, String label, bool isSecure,
    String? Function(String?) validator) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 5.0),
    child: FormBuilderTextField(
      name: name,
      obscureText: isSecure,
      // controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
      validator: validator,
    ),
  );
}
