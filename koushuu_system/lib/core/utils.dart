import 'dart:convert';
import 'package:intl/intl.dart';

class Utils {
  static Map<String, dynamic> deepMergeMap(
      Map<String, dynamic> a, Map<String, dynamic>? b) {
    if (b == null) {
      return a;
    }
    // ignore: unnecessary_null_comparison
    if (a == null) {
      return b;
    }
    b.forEach((k, v) {
      if (!a.containsKey(k)) {
        a[k] = v;
      } else {
        if (a[k] is Map) {
          deepMergeMap(a[k], b[k]);
        } else {
          a[k] = b[k];
        }
      }
    });

    return a;
  }

  static String doEncrypt64(String rawString) {
    final bytes = utf8.encode(rawString);
    final base64Str = base64.encode(bytes);
    return base64Str;
  }

  static String doDecrypt64(String encryptedString) {
    final base64Str = base64.decode(encryptedString);
    final decodedStr = utf8.decode(base64Str);
    return decodedStr;
  }

  static Map<String, dynamic> extractData(String strData) {
    Map<String, dynamic> retMap = {};
    var spData = strData.split("&");
    for (String it in spData) {
      var splittedData = it.split("=");
      retMap.addAll({splittedData[0]: splittedData[1]});
    }
    return retMap;
  }

  static String getKeyValueFromMapList(
    String whereKeyName,
    String whereKeyValue,
    String getKeyName,
    List<Map<String, dynamic>> lst,
  ) {
    String val = "";

    for (var mp in lst) {
      // 条件を満たすエントリを確認
      // print("$mp[whereKeyName], $whereKeyValue");
      if (mp[whereKeyName].toString() == whereKeyValue) {
        val = mp[getKeyName]?.toString() ?? ""; // null安全に取得
        break; // 条件に一致したらループを終了
      }
    }
    return val;
  }

  static String formatWithComma(num value) {
    final formatter = NumberFormat('#,###');
    return formatter.format(value);
  }
}
