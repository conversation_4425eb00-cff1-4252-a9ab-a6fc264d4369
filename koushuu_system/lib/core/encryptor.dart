import 'package:encrypt/encrypt.dart' as encrypt;
import 'dart:convert';
import 'package:koushuu_system/core/env_config.dart'; // For URL-safe Base64 encoding

class Encryptor {
  static String encryptData(String plainText) {
    final key = encrypt.Key.fromUtf8(EnvConfig.ENCKEY);
    final iv = encrypt.IV.fromUtf8(EnvConfig.ENCIV);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    final encryptedData = encrypter.encrypt(plainText, iv: iv);

    //return encryptedData.base64;
    // Use URL-safe Base64 encoding
    return base64UrlEncode(encryptedData.bytes);
  }

  static String decryptData(String encryptedText) {
    final key = encrypt.Key.fromUtf8(EnvConfig.ENCKEY);
    final iv = encrypt.IV.fromUtf8(EnvConfig.ENCIV);
    final encrypter = encrypt.Encrypter(encrypt.AES(key));
    //final decryptedData = encrypter.decrypt64(encryptedText, iv: iv);
    // Decode URL-safe Base64 before decryption
    final decodedBytes = base64Url.decode(encryptedText);
    final decryptedData =
        encrypter.decryptBytes(encrypt.Encrypted(decodedBytes), iv: iv);

    // return decryptedData;
    return utf8.decode(decryptedData);
  }
}
