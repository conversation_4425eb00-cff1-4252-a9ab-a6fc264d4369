import 'package:flutter_riverpod/flutter_riverpod.dart';

class AppState {
  final String? applicationId;
  final String? uketsukeId;
  final String? participantId;
  final String? courseCode;
  final String? courseName;
  final String? loginEmail;
  final String? loginName;
  final String? token;
  final String? step;
  final DateTime? expiry;

  AppState({
    required this.courseCode,
    required this.courseName,
    required this.loginEmail,
    required this.loginName,
    required this.applicationId,
    required this.uketsukeId,
    required this.participantId,
    required this.token,
    required this.expiry,
    required this.step,
  });

  // Add methods to copy and update the state if needed
  AppState copyWith(
      {String? loginEmail,
      String? loginName,
      String? applicationId,
      String? uketsukeId,
      String? participantId,
      String? step,
      String? courseCode,
      String? courseName,
      String? token,
      DateTime? expiry}) {
    return AppState(
      loginEmail: loginEmail ?? this.loginEmail,
      loginName: loginName ?? this.loginName,
      applicationId: applicationId ?? this.applicationId,
      token: token ?? this.token,
      expiry: expiry ?? this.expiry,
      uketsukeId: uketsukeId ?? this.uketsukeId,
      courseCode: courseCode ?? this.courseCode,
      courseName: courseName ?? this.courseName,
      step: step ?? this.step,
      participantId: participantId ?? this.participantId,
    );
  }
}

// Create a StateNotifier to manage the state
class AppStateNotifier extends StateNotifier<AppState> {
  AppStateNotifier()
      : super(AppState(
            applicationId: '',
            token: '',
            expiry: null,
            uketsukeId: '',
            courseCode: '',
            courseName: '',
            loginEmail: '',
            loginName: '',
            step: '',
            participantId: ''));

  // Add methods to update the state
  void updateApplicationId(String newValue) {
    state = state.copyWith(applicationId: newValue);
  } // Add methods to update the state

  void updateToken(String newValue) {
    state = state.copyWith(token: newValue);
  } // Add methods to update the state

  void updateTokenExpiry(DateTime newValue) {
    state = state.copyWith(expiry: newValue);
  }

  void updateUketsukeId(String newValue) {
    state = state.copyWith(uketsukeId: newValue);
  }

  void updateParticipantId(String newValue) {
    state = state.copyWith(participantId: newValue);
  }

  void updateLoginName(String newValue) {
    state = state.copyWith(loginName: newValue);
  }

  void updateLogiEmail(String newValue) {
    state = state.copyWith(loginEmail: newValue);
  }

  void updateStep(String newValue) {
    state = state.copyWith(step: newValue);
  }
}

// Create a provider for the AppStateNotifier
final appStateProvider =
    StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  return AppStateNotifier();
});
